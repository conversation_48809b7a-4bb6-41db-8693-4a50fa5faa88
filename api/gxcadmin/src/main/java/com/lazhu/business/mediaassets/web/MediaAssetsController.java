package com.lazhu.business.mediaassets.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.llmvoicejoin.entity.LlmVoiceJoin;
import com.lazhu.business.llmvoicejoin.service.LlmVoiceJoinService;
import com.lazhu.business.mediaassets.dto.MediaAssetUpdateRequest;
import com.lazhu.business.mediaassets.dto.MediaAssetsDTO;
import com.lazhu.business.mediaassets.dto.MediaAssetsQuery;
import com.lazhu.business.mediaassets.entity.MediaAssetsCategory;
import com.lazhu.business.mediaassets.entity.MediaAssetsCategoryQuery;
import com.lazhu.business.mediaassets.service.MediaAssetsCategoryJoinService;
import com.lazhu.business.mediaassets.service.MediaAssetsCategoryService;
import com.lazhu.business.mediaassets.strategy.MediaAssetSourceStrategy;
import com.lazhu.business.mediaassets.strategy.MediaAssetSourceStrategyFactory;
import com.lazhu.common.enums.MediaAssetSourceEnum;
import com.lazhu.common.enums.MediaAssetTypeEnum;
import com.lazhu.baseai.llm.dto.VideoCreateResp;
import com.lazhu.business.llmconfig.service.LLMService;
import com.lazhu.support.base.BaseService;
import com.lazhu.support.response.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 统一素材管理控制器
 * </p>
 *
 * @since 2025-07-25
 */
@RestController
@RequestMapping(value = "/bus/assets")
public class MediaAssetsController {

    @Autowired
    private MediaAssetSourceStrategyFactory factory;


    @Autowired
    private MediaAssetsCategoryService mediaAssetsCategoryService;


    @Autowired
    private MediaAssetsCategoryJoinService categoryRelService;

    @Autowired
    private LlmVoiceJoinService llmVoiceJoinService;

    @Autowired
    private LLMService llmservice;

    /**
     * 统一查询接口，支持按类型查询不同素材
     *
     * @return 统一素材列表
     */
    @PostMapping("/read/list")
    public PageResponse<MediaAssetsDTO> list(@RequestBody MediaAssetsQuery query) {
        Page<MediaAssetsDTO> resultPage = BaseService.getPage(BeanUtil.beanToMap(query));
        List<MediaAssetsDTO> unifiedAssets = new ArrayList<>();
        // 根据分类筛选素材ID
        if (query.getCategoryId() != null) {
            List<Long> filteredAssetIds = categoryRelService.queryCategoryAssetIds(query.getCategoryId(), query.getAssetsType(), Convert.toInt(query.getSource()));
            if (CollUtil.isEmpty(filteredAssetIds)) {
                resultPage.setRecords(unifiedAssets);
                resultPage.setTotal(0);
                return new PageResponseBuilder<>(resultPage).success().builder();
            }
            query.setAssetIds(filteredAssetIds);
        }
        // 查询指定类型
        MediaAssetSourceStrategy strategy = factory.getSourceStrategy(query.getSource());
        Page<MediaAssetsDTO> assets = strategy.queryAssets(query);
        return new PageResponseBuilder<>(assets).success().builder();
    }

    /**
     * 统计每类素材的数量
     *
     * @return 素材类型及其数量的映射
     */
    @PostMapping("/count")
    public SimpleResponse<Long> countAssetsByType(@RequestBody MediaAssetsQuery query) {
        MediaAssetSourceStrategy strategy = factory.getSourceStrategy(query.getSource());
        long count = strategy.countAssets(query);
        return new SimpleResponseBuilder<>(count).success().bulider();
    }

    /**
     * 素材上传
     *
     * @param uploadRequest 上传请求
     * @return 上传结果
     */
    @PostMapping("/add")
    public SimpleResponse<MediaAssetsDTO> upload(@RequestBody MediaAssetUpdateRequest uploadRequest) {
        Assert.notNull(uploadRequest.getType(), "素材类型不能为空");
        Assert.notNull(uploadRequest.getTitle(), "素材标题不能为空");
        Assert.notNull(uploadRequest.getMediaUrl(), "文件URL不能为空");

        MediaAssetTypeEnum assetTypeEnum = MediaAssetTypeEnum.getMediaAssetType(uploadRequest.getType());
        Assert.notNull(assetTypeEnum, "无效的素材类型");
        MediaAssetSourceStrategy strategy = factory.getSourceStrategy(uploadRequest.getSource());
        MediaAssetsDTO dto = new MediaAssetsDTO();
        dto.setTitle(uploadRequest.getTitle());
        dto.setUrl(uploadRequest.getMediaUrl());
        dto.setAssetsType(uploadRequest.getType());
        MediaAssetsDTO result = strategy.uploadAsset(dto);
        // 设置分类关联
        if (CollUtil.isNotEmpty(uploadRequest.getCategoryIds())) {
            categoryRelService.setAssetCategories(result.getId(), uploadRequest.getType(), uploadRequest.getCategoryIds(), Convert.toInt(uploadRequest.getSource()));
        }
        if (uploadRequest.getActorId() != null && MediaAssetTypeEnum.AUDIO.getType().equals(uploadRequest.getType())) {
            //上传音色
            //上传音色
            String firstLetter = PinyinUtil.getFirstLetter(uploadRequest.getTitle(), "");
            if (firstLetter.length() > 10) {
                firstLetter = firstLetter.substring(0, 10);
            }
            VideoCreateResp voice = llmservice.createVoice(firstLetter, uploadRequest.getMediaUrl());

            //保存关联记录
            LlmVoiceJoin llmVoiceJoin = new LlmVoiceJoin();
            llmVoiceJoin.setAssetsType(2);
            llmVoiceJoin.setVoiceAssetsId(result.getId());
            llmVoiceJoin.setVoiceId(voice.getVoiceId());
            llmVoiceJoin.setLlmId(voice.getLlmId());
            llmVoiceJoinService.save(llmVoiceJoin);
        }
        return new SimpleResponseBuilder<>(result).success().bulider();
    }

    /**
     * 素材更新
     *
     * @param updateRequest 更新请求
     * @return 更新结果
     */
    @PostMapping("/update")
    public SimpleResponse<Boolean> update(@RequestBody MediaAssetUpdateRequest updateRequest) {
        Assert.notNull(updateRequest.getId(), "素材ID不能为空");
        Assert.notNull(updateRequest.getType(), "素材类型不能为空");
        Assert.notNull(updateRequest.getTitle(), "素材标题不能为空");

        MediaAssetTypeEnum assetTypeEnum = MediaAssetTypeEnum.getMediaAssetType(updateRequest.getType());
        Assert.notNull(assetTypeEnum, "无效的素材类型");

        MediaAssetSourceStrategy strategy = factory.getSourceStrategy(updateRequest.getSource());
        Boolean result = strategy.updateAsset(updateRequest.getId(), updateRequest.getTitle(), updateRequest.getType());

        return new SimpleResponseBuilder<>(result).success().bulider();
    }

    /**
     * 删除素材(支持批量)
     *
     * @param ids  素材ID列表
     * @param type 素材类型
     * @return 删除结果
     */
    @PostMapping("/delete")
    public SimpleResponse<Boolean> deleteBatch(@RequestBody List<Long> ids, @RequestParam String type, @RequestParam String source) {
        Assert.notEmpty(ids, "素材ID列表不能为空");
        Assert.notNull(type, "素材类型不能为空");

        MediaAssetTypeEnum assetTypeEnum = MediaAssetTypeEnum.getMediaAssetType(type);
        Assert.notNull(assetTypeEnum, "无效的素材类型");

        MediaAssetSourceStrategy strategy = factory.getSourceStrategy(source);
        Boolean result = strategy.batchDeleteAssets(ids, type);
        // 删除分类关联
        if (result) {
            categoryRelService.deleteByAsset(ids, type, Convert.toInt(source));
        }
        return new SimpleResponseBuilder<>(result).success().bulider();
    }

    /**
     * 查询全部分类
     */
    @PostMapping("/read/categories")
    public ArrayResponse<MediaAssetsCategory> queryCategories(@RequestBody MediaAssetsCategoryQuery query) {
        List<MediaAssetsCategory> page = mediaAssetsCategoryService.queryList(BeanUtil.beanToMap(query));
        return new ArrayResponseBuilder<>(page).success().bulider();
    }

    /**
     * 设置分类 (支持批量)
     */
    @PostMapping("/setCategories")
    public SimpleResponse<String> updateAssetCategories(@RequestBody MediaAssetUpdateRequest updateRequest) {
        Assert.notNull(updateRequest.getType(), "素材类型不能为空");

        categoryRelService.setAssetCategories(updateRequest.getIds(), updateRequest.getType(), updateRequest.getCategoryIds(), Convert.toInt(updateRequest.getSource()));
        return new SimpleResponseBuilder<>("操作成功").success().bulider();
    }

}