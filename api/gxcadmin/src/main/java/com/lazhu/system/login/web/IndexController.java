package com.lazhu.system.login.web;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import com.lazhu.support.response.ArrayResponse;
import com.lazhu.support.response.ArrayResponseBuilder;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.support.security.LZUserDetail;
import com.lazhu.support.security.PasswordEncoder;
import com.lazhu.support.util.WebUtil;
import com.lazhu.system.dic.entity.Dic;
import com.lazhu.system.dic.entity.DicQuery;
import com.lazhu.system.dic.entity.DicVO;
import com.lazhu.system.dic.service.DicService;
import com.lazhu.system.login.entity.MenuItem;
import com.lazhu.system.login.entity.UserInfo;
import com.lazhu.system.login.entity.UserPsd;
import com.lazhu.system.sysmenus.service.SysMenusService;
import com.lazhu.system.sysperms.service.SysPermsService;
import com.lazhu.system.sysusers.entity.SysUsers;
import com.lazhu.system.sysusers.service.SysUsersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping(value = "/system/info")
public class IndexController {

	@Autowired
	SysMenusService sysMenusService;

	@Autowired
	SysUsersService sysUsersService;

	@Autowired
	SysPermsService sysPermsService;

	@Autowired
	DicService dicService;

	@Autowired
	PasswordEncoder encode;

	@GetMapping("/logout")
	public SimpleResponse<String> login() {
		StpUtil.logout();
		WebUtil.removeCurUser();
		return new SimpleResponseBuilder<String>(null).success().bulider();
	}

	@GetMapping("/menus")
	public ArrayResponse<MenuItem> getMenus() {
		Long curUser = WebUtil.getCurUser();
		boolean curUserSuper = WebUtil.getCurUserSuper();
		List<MenuItem> im = sysMenusService.queryIndexMenus(curUser, curUserSuper);
		return new ArrayResponseBuilder<MenuItem>(im).success().bulider();
	}

	@GetMapping("/userinfo")
	public SimpleResponse<UserInfo> getInfo() {
		Long curUser = WebUtil.getCurUser();
		SysUsers user = sysUsersService.queryById(curUser);
		UserInfo userInfo = new UserInfo(user.getId(), user.getUserName(), null);
		return new SimpleResponseBuilder<UserInfo>(userInfo).success().bulider();
	}

	@GetMapping("/perms")
	public ArrayResponse<String> getPermSign() {
		LZUserDetail curUserDetail = WebUtil.getCurUserDetail();
		boolean curUserSuper = WebUtil.getCurUserSuper();
		List<String> permSign = sysPermsService.queryPermSign(curUserDetail.getUserId(), curUserSuper);
		return new ArrayResponseBuilder<String>(permSign).success().bulider();
	}

	@GetMapping(value = "/dics")
	public SimpleResponse<HashMap<String, List<DicVO>>> queryAll() {
		DicQuery param = new DicQuery();
		param.setAscs("order_num");
		List<Dic> list = dicService.queryList(BeanUtil.beanToMap(param));
		//根据code分组
		Map<String, List<DicVO>> group = list.stream()
				.map(e -> new DicVO(e.getCode(), e.getValue(), e.getTxt()))
				.collect(Collectors.groupingBy(DicVO::getCode));
		return new SimpleResponseBuilder<>(new HashMap<>(group)).success().bulider();
	}

	@PostMapping("/upd/password")
	public SimpleResponse<String> updPassword(@RequestBody UserPsd psd) {
		Assert.notNull(psd.getPassword(), "password need");
		Long curUser = WebUtil.getCurUser();
		SysUsers user = sysUsersService.queryById(curUser);
		if (!encode.matches(psd.getOldPassword(), user.getPassword())) {
			return new SimpleResponseBuilder<String>(null).error("旧密码不正确！").bulider();
		}
		String ps = encode.encode(psd.getPassword());
		sysUsersService.updPsd(curUser, ps);
		return new SimpleResponseBuilder<String>(null).success().bulider();
	}

}
