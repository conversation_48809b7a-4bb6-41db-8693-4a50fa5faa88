server:
  tomcat:
    uri-encoding: UTF-8
  port: 8080


spring:
  application:
    name: gxcapi
  redis:
    redisson:
      config: classpath:config/redisson-dev.yml
  data:
    mongodb:
      uri: **********************************************************************************************************************************
  servlet:
    multipart:
      # 禁用spring的文件上传,使用系统自带
      enabled: false
  token:
    secret: BFp6zURNd3ubNMkEZ5KmtQRXEhXt5xpP
    # token过期时间 1天
    expire-time: 86400
    access-url:
      - /**
      - /api/login
      - /api/third/video/synthesize/callback

management:
  health:
    mongo:
      enabled: false



lz:
  upload:
    webSite: 127.0.0.1:8080/upload
    path: /data/upload
    dempFile: /data/demp
    base: /data/
    type: oss
    ossBucket: lazhuyun
    ossWebSite: https://ins-file.lazhuyun.cn
    ossBaseKey: test


datasource:
  driverClassName: com.mysql.cj.jdbc.Driver
  url: **********************************************************************************************************************************************************************************************
  username: galaxy_creator
  password: wts111!!
  #--------------------------
  # 下面为连接池的补充设置，应用到上面所有数据源中
  # 初始化大小，最小，最大
  initialSize: 1
  minIdle: 5
  maxActive: 200
  # 配置获取连接等待超时的时间
  maxWait: 60000
  # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
  timeBetweenEvictionRunsMillis: 60000
  # 配置一个连接在池中最小生存的时间，单位是毫秒
  minEvictableIdleTimeMillis: 300000
  validationQuery: SELECT 1 FROM DUAL
  testWhileIdle: true
  testOnBorrow: false
  testOnReturn: false
  #打开PSCache，并且指定每个连接上PSCache的大小
  poolPreparedStatements: false
  #spring.datasource.maxPoolPreparedStatementPerConnectionSize=20
  # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙 log4j用于日志
  filters: stat
  # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
  connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

mybatis-plus:
  # 如果是放在src/main/java目录下 classpath:com/yourpackage/*/mapper/*Mapper.xml
  # 如果是放在resource目录 classpath:/mapper/*Mapper.xml
  mapper-locations: classpath*:com/lazhu/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.lazhu.**.entity
  global-config:
    #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
    #id-type: 0
    #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
    field-strategy: 1
    #驼峰下划线转换
    db-column-underline: true
    #刷新mapper 调试神器
    refresh-mapper: true
    #数据库大写下划线转换
    capital-mode: true
    # Sequence序列接口实现类配置
    #key-generator: com.baomidou.mybatisplus.incrementer.OracleKeyGenerator
    #逻辑删除配置（下面3个配置）
    #logic-delete-value: 1
    #logic-not-delete-value: 0
    #sql-injector: com.baomidou.mybatisplus.mapper.LogicSqlInjector
    #自定义填充策略接口实现
    #meta-object-handler: com.baomidou.springboot.MyMetaObjectHandler
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
    cache-enabled: false
    #配置JdbcTypeForNull
    jdbc-type-for-null: 'null'

ai:
  # 请求接口 （视频口播文案生成+修改+图文修改）
  url: http://**************/v1/chat-messages
  # 图文创作url  图文修改用上面的那个url
  text_create_url: http://**************/v1/workflows/run
  # 提示词优化token
  prompt_create_auth: Bearer app-l6UEwklXJuqYF8cSQEoNTvg8
  #图文文本生成token
  text_create_token: Bearer app-8NkqQVUuFXkKQsOwLOIZ1Ozn
  #图文修改token
  text_modify_token: Bearer app-I6cjLlBoglHhSTAD0iuejfg8
  #视频口播文案生成token + 视频口播文案修改
  video_text_create_token: Bearer app-9qh6yejZNw1Ht0IPZvxKCkAq
  #tts 优化url
  tts_optimization_url: http://**************/v1/workflows/run
  # tts  优化token
  tts_optimization_auth: Bearer app-zpaCirB7XVa9qmVdEY0nt814
  # 选题创作url
  topic_creation_url: http://**************/v1/chat-messages
  # 选题创作token
  topic_creation_auth: Bearer app-DGDyM6AgXvpO2MTyxwWgkoMg