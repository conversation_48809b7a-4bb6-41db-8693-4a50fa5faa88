package com.lazhu.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.baseai.tool.txt.TopicCreationTool;
import com.lazhu.business.actor.entity.Actor;
import com.lazhu.business.actor.service.ActorService;
import com.lazhu.mongo.entity.HotNews;
import com.lazhu.mongo.service.HotNewsService;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.ArrayResponse;
import com.lazhu.support.response.ArrayResponseBuilder;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.PageResponseBuilder;
import com.lazhu.mongo.entity.HotTopicsQuery;
import com.lazhu.web.dto.HotTopicsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 热点话题api
 */
@RestController
@RequestMapping("/api/ht")
public class HotTopicsController extends AbstractController {

    @Autowired
    private HotNewsService hotNewsService;

    @Autowired
    private TopicCreationTool topicCreationTool;

    @Autowired
    private ActorService actorService;


    /**
     * 获取所有数据来源
     *
     * @return 数据来源列表
     */
    @GetMapping("/sources")
    public Mono<ArrayResponse<String>> getAllSources() {
        return hotNewsService.findAllSources()
                .collectList()
                .map(source -> new ArrayResponseBuilder<>(source).success().bulider());
    }


    /**
     * 获取热点话题（从MongoDB查询）- 优化版本使用实体类参数
     *
     * @param query 查询参数实体
     * @return 列表
     */
    @GetMapping("/list")
    public Mono<PageResponse<HotTopicsDTO>> hotTopicsList( HotTopicsQuery query) {
        
        Flux<HotNews> hotNews = hotNewsService.findHotNewsAdvanced(query);
        Mono<Long> total = hotNewsService.countHotNewsAdvanced(query);

        return hotNews.map(HotTopicsDTO::convertToHotTopics)
                .collectList()
                .zipWith(total)
                .map(tuple -> {
                    Page<HotTopicsDTO> p = new Page<>(query.getPage(), query.getSize(), tuple.getT2());
                    p.setRecords(tuple.getT1());
                    return new PageResponseBuilder<>(p).success().builder();
                });
    }

    /**
     * 生成选题
     *
     * @param hotTopic 热点话题
     * @param actorId 角色ID
     * @return 选题列表
     */
    @PostMapping("/generate-topics")
    public Mono<ArrayResponse<String>> generateTopics(
            @RequestParam("hotTopic") String hotTopic,
            @RequestParam("actorId") Long actorId) {

        return Mono.fromCallable(() -> {
            // 根据actorId查询角色信息
            Actor actor = actorService.queryById(actorId);
            if (actor == null) {
                throw new RuntimeException("角色不存在，actorId: " + actorId);
            }

            // 获取角色简介
            String profile = actor.getProfile();
            if (profile == null || profile.trim().isEmpty()) {
                throw new RuntimeException("角色简介为空，无法生成选题");
            }

            // 调用选题创作工具生成选题
            List<String> topics = topicCreationTool.createTopics(hotTopic, profile);
            
            return new ArrayResponseBuilder<>(topics).success().bulider();
        });
    }


}
