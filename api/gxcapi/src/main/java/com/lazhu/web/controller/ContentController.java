package com.lazhu.web.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.baseai.llm.combine.VideoCombineResult;
import com.lazhu.baseai.llm.dto.LLMBaseRequest;
import com.lazhu.baseai.llm.dto.LLMBaseResponse;
import com.lazhu.baseai.llm.dto.TextCreationReq;
import com.lazhu.baseai.llm.dto.TextCreationResult;
import com.lazhu.business.actor.entity.Actor;
import com.lazhu.business.actor.service.ActorService;
import com.lazhu.business.content.dto.ContentCreationReq;
import com.lazhu.business.content.dto.ContentDTO;
import com.lazhu.business.content.dto.ContentMetaData;
import com.lazhu.business.content.entity.Content;
import com.lazhu.business.content.entity.ContentQuery;
import com.lazhu.business.content.service.ContentService;
import com.lazhu.business.llmconfig.service.LLMService;
import com.lazhu.business.tasks.entity.Tasks;
import com.lazhu.business.tasks.entity.TasksQuery;
import com.lazhu.business.tasks.entity.VideoSynthTaskResult;
import com.lazhu.business.tasks.service.TasksService;
import com.lazhu.common.RedisKeyConst;
import com.lazhu.common.enums.ContentTypeEnum;
import com.lazhu.common.exception.BusinessException;
import com.lazhu.common.exception.PermissionException;
import com.lazhu.common.media.MediaUtil;
import com.lazhu.common.utils.OssUtil;
import com.lazhu.support.base.AbstractController;
import com.lazhu.support.response.PageResponse;
import com.lazhu.support.response.PageResponseBuilder;
import com.lazhu.support.response.SimpleResponse;
import com.lazhu.support.response.SimpleResponseBuilder;
import com.lazhu.utils.WebTool;
import com.lazhu.web.service.ApIContentService;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 内容管理
 */
@RestController
@RequestMapping("/api/content")
public class ContentController extends AbstractController {


    @Autowired
    private LLMService lLmService;

    @Autowired
    private ContentService contentService;

    @Autowired
    private OssUtil ossUtil;


    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ActorService actorService;

    @Autowired
    private ApIContentService videoService;

    @Autowired
    private TasksService tasksService;

    private static final ExecutorService virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();

    /**
     * 保存内容
     */
    @PostMapping("/save")
    public Mono<SimpleResponse<ContentDTO>> save(@RequestBody ContentDTO contentDto) {
        Long userId = WebTool.getUserId();
        return Mono.fromCallable(() -> {
            contentDto.setUserId(userId);
            // 如果是视频，则获取封面图和时长
            if (contentDto.getContentType().equals(ContentTypeEnum.VIDEO.getType())
                    && StrUtil.isNotBlank(contentDto.getMediaUrl())) {
                if (CollUtil.isEmpty(contentDto.getCoverImg())) {
                    File coverImageFile = MediaUtil.getVideoCover(contentDto.getMediaUrl());
                    String coverImage = ossUtil.upload(coverImageFile);
                    contentDto.setCoverImg(Collections.singletonList(coverImage));
                }
                Long duration = MediaUtil.getDuration(contentDto.getMediaUrl());
                contentDto.setDuration(duration);
            }
            Content content = new Content();
            BeanUtil.copyProperties(contentDto, content);
            content.setCoverImg(JSONArray.toJSONString(contentDto.getCoverImg()));
            content.setMetaData(JSONObject.toJSONString(contentDto.getMetaData()));
            if (content.getId() == null) {
                contentService.save(content);
            } else {
                contentService.update(content);
            }
            contentDto.setId(content.getId());
            return new SimpleResponseBuilder<>(contentDto).success().bulider();
        });
    }

    @GetMapping("/detail")
    public Mono<SimpleResponse<ContentDTO>> detail(@RequestParam Long id) {
        Long userId = WebTool.getUserId();
        return Mono.fromCallable(() -> {
            Content content = contentService.queryById(id);
            if (content == null) {
                throw new BusinessException("内容不存在");
            }
            if (!content.getUserId().equals(userId)) {
                throw new PermissionException("无权限");
            }
            return content;
        }).map(content -> {
            ContentDTO contentDto = new ContentDTO();
            BeanUtil.copyProperties(content, contentDto, "coverImg", "metaData");
            contentDto.setCoverImg(JSONArray.parseArray(content.getCoverImg(), String.class));
            contentDto.setMetaData(JSONObject.parseObject(content.getMetaData()));
            Actor actor = actorService.queryById(content.getActorId());
            contentDto.setActorName(actor != null ? actor.getNickName() : "");
            return new SimpleResponseBuilder<>(contentDto).success().bulider();
        });
    }

    /**
     * 删除
     */
    @PostMapping("/del")
    public Mono<SimpleResponse<String>> del(@RequestBody List<Long> ids) {
        return Mono.fromCallable(() -> {
            contentService.batchDelById(ids);
            return new SimpleResponseBuilder<>("").success().bulider();
        });
    }

    /**
     * @return 内容列表
     */
    @GetMapping("/list")
    public Mono<PageResponse<ContentDTO>> contentList(ContentQuery query) {
        return Mono.fromCallable(() -> {
            query.setUserId(WebTool.getUserId());
            if (StrUtil.isBlank(query.getDescs())) {
                query.setDescs("update_time");
            }
            Page<Content> page = contentService.queryPage(BeanUtil.beanToMap(query));
            //查询角色
            List<Long> actorIds = page.getRecords().stream().map(Content::getActorId).toList();
            List<Actor> actors = actorService.selectByIds(actorIds);
            Map<Long, Actor> actorMap = actors.stream().collect(Collectors.toMap(Actor::getId, e -> e));
            //封装数据
            List<ContentDTO> contentDTOList = page.getRecords().stream().map(content -> {
                ContentDTO contentDto = new ContentDTO();
                BeanUtil.copyProperties(content, contentDto, "coverImg", "metaData");
                contentDto.setCoverImg(JSONArray.parseArray(content.getCoverImg(), String.class));
                contentDto.setMetaData(JSONObject.parseObject(content.getMetaData()));
                Actor actor = actorMap.get(content.getActorId());
                contentDto.setActorName(actor != null ? actor.getNickName() : "");
                return contentDto;
            }).toList();
            Page<ContentDTO> contentDTOPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
            contentDTOPage.setRecords(contentDTOList);
            return new PageResponseBuilder<>(contentDTOPage).success().builder();
        });
    }

    /**
     * 文本创作
     */
    @PostMapping("/txt/create")
    public Mono<SimpleResponse<JSONObject>> createTxt(@RequestBody ContentCreationReq.TextRequest param) {
        return Mono.fromCallable(() -> {
            // 组装参数
            LLMBaseRequest<TextCreationReq> req = new LLMBaseRequest<>();
            TextCreationReq textCreationReq = contentService.fromTextCreateRequest( param);

            req.setParams(textCreationReq);
            RMap<String, String> map = redissonClient.getMap(RedisKeyConst.TEXT_CREATION_TASK);
            String taskId = IdUtil.simpleUUID();
            JSONObject object = new JSONObject();
            object.put("taskId", taskId);
            object.put("done", false);
            map.put(taskId, object.toJSONString());
            // 大模型创作
            virtualThreadExecutor.execute(() -> {
                RMap<String, String> info = redissonClient.getMap(RedisKeyConst.TEXT_CREATION_TASK);
                String s = info.get(taskId);
                JSONObject taskInfo;
                if (StrUtil.isEmpty(s)) {
                    taskInfo = new JSONObject();
                    taskInfo.put("taskId", taskId);
                } else {
                    taskInfo = JSONObject.parseObject(s);
                }
                try {
                    LLMBaseResponse<List<TextCreationResult>> llmResponse = lLmService.createTxt(req);
                    taskInfo.put("conversationId", llmResponse.getConversationId());
                    taskInfo.put("list", llmResponse.getBody());
                    taskInfo.put("status", 1);
                } catch (Exception e) {
                    taskInfo.put("status", 0);
                    taskInfo.put("errorMsg", e.getMessage());
                }
                taskInfo.put("done", true);
                info.put(taskId, JSONObject.toJSONString(taskInfo));
            });
            return new SimpleResponseBuilder<>(object).success().bulider();
        });
    }

    /**
     * 文本微调
     */
    @PostMapping("/txt/finetune")
    public Mono<SimpleResponse<JSONObject>> finetuneTxt(@RequestBody ContentCreationReq.TextFineTuneRequest param) {
        return Mono.fromCallable(() -> {
            // 组装参数
            LLMBaseRequest<TextCreationReq> req = new LLMBaseRequest<>();
            TextCreationReq textCreationReq = contentService.TextFineTuneRequest(param);
            req.setParams(textCreationReq);
            req.setConversationId(param.conversationId());

            RMap<String, String> map = redissonClient.getMap(RedisKeyConst.TEXT_MODIFY_TASK);
            String taskId = IdUtil.simpleUUID();
            JSONObject object = new JSONObject();
            object.put("taskId", taskId);
            object.put("done", false);
            map.put(taskId, object.toJSONString());

            // 大模型创作
            virtualThreadExecutor.execute(() -> {
                RMap<String, String> info = redissonClient.getMap(RedisKeyConst.TEXT_MODIFY_TASK);
                String s = info.get(taskId);
                JSONObject taskInfo;
                if (StrUtil.isEmpty(s)) {
                    taskInfo = new JSONObject();
                    taskInfo.put("taskId", taskId);
                } else {
                    taskInfo = JSONObject.parseObject(s);
                }
                try {
                    LLMBaseResponse<List<TextCreationResult>> llmResponse = lLmService.finetuneTxt(req);
                    taskInfo.put("conversationId", llmResponse.getConversationId());
                    taskInfo.put("content", CollUtil.getFirst(llmResponse.getBody()));
                    taskInfo.put("status", 1);
                } catch (Exception e) {
                    taskInfo.put("status", 0);
                    taskInfo.put("errorMsg", e.getMessage());
                }
                taskInfo.put("done", true);
                info.put(taskId, JSONObject.toJSONString(taskInfo));
            });

            return new SimpleResponseBuilder<>(object).success().bulider();
        });
    }

    /**
     * 查询文本创作任务状态
     *
     * @param taskId 任务ID
     * @param type   任务类型 1 文本生成任务 2 文本修改任务
     */
    @GetMapping("/text/task/query")
    public SimpleResponse<JSONObject> queryTextTask(@RequestParam String taskId, @RequestParam String type) {
        RMap<String, String> taskInfo;
        if ("1".equals(type)) {
            taskInfo = redissonClient.getMap(RedisKeyConst.TEXT_CREATION_TASK);
        } else {
            taskInfo = redissonClient.getMap(RedisKeyConst.TEXT_MODIFY_TASK);
        }
        String s = taskInfo.get(taskId);
        if (StrUtil.isEmpty(s)) {
            return new SimpleResponseBuilder<JSONObject>().error("任务不存在").bulider();
        }
        JSONObject jsonObject = JSONObject.parseObject(s);
        return new SimpleResponseBuilder<JSONObject>(jsonObject).success().bulider();
    }


    /**
     * 视频创作
     */
    @PostMapping("/video/create")
    public SimpleResponse<VideoCombineResult> createVideo(@RequestBody ContentCreationReq.VideoRequest param) {
        logger.info("===> 视频创作开始，参数：{}", param.toString());
        ContentDTO contentDTO = param.getContentDTO();
        String taskId;
        if (contentDTO != null && contentDTO.getId() == null) {
            //保存草稿
            Content content = new Content();
            BeanUtil.copyProperties(contentDTO, content, "coverImg", "metaData");
            content.setUserId(WebTool.getUserId());
            content.setActorId(contentDTO.getActorId());
            content.setTitle(param.getTitle());
            content.setStatus(0);
            content.setContent(param.getContent());
            content.setMetaData(JSONObject.toJSONString(contentDTO.getMetaData()));
            contentService.save(content);
            taskId = content.getId().toString();
        } else {
            taskId = IdUtil.simpleUUID();
        }
        //返回结果
        VideoCombineResult result = new VideoCombineResult();
        result.setTaskId(taskId);
        result.setDone(false);
        //创建视频
        videoService.createVideo(param, taskId);
        return new SimpleResponseBuilder<>(result).success().bulider();
    }

    /**
     * 重新创作
     */
    @GetMapping("/video/create/retry")
    public SimpleResponse<VideoCombineResult> createVideoRetry(Long id) {
        ContentCreationReq.VideoRequest param = new ContentCreationReq.VideoRequest();
        Content content = contentService.queryById(id);
        param.setTitle(content.getTitle());
        param.setContent(content.getContent());
        ContentMetaData metaData = JSONObject.parseObject(content.getMetaData(), ContentMetaData.class);
        param.setVideoSource(metaData.getVideoSource());
        param.setVideoId(metaData.getVideoId());
        param.setAudioSource(metaData.getAudioSource());
        param.setAudioId(metaData.getAudioId());

        //返回结果
        VideoCombineResult result = new VideoCombineResult();
        result.setTaskId(id.toString());
        result.setDone(false);
        //创建视频
        videoService.createVideo(param, id.toString());
        return new SimpleResponseBuilder<>(result).success().bulider();
    }

    /**
     * 视频合成任务查询 (轮询)
     */
    @GetMapping("/video/task/query")
    public Mono<SimpleResponse<VideoCombineResult>> queryVideoTask(@RequestParam("taskId") String taskId) {
        return Mono.fromCallable(() -> {
            TasksQuery query = new TasksQuery();
            query.setContentId(Convert.toLong(taskId));
            query.setType(3);
            List<Tasks> tasks = tasksService.queryList(BeanUtil.beanToMap(query));
            //有任意条失败即为失败
            boolean failed = tasks.stream().anyMatch(e -> e.getStatus() == 3);
            if (failed) {
                Tasks tasks1 = tasks.stream().filter(e -> e.getStatus() == 3).findFirst().orElse(null);
                VideoSynthTaskResult videoSynthesizeTask = JSONObject.parseObject(tasks1.getResult(), VideoSynthTaskResult.class);
                VideoCombineResult result = new VideoCombineResult();
                result.setErrorMsg(videoSynthesizeTask.getMessage());
                result.setTaskId(taskId);
                result.setDone(true);
                result.setResult("0");
                return result;
            }
            // 全部成功
            boolean success = CollUtil.isNotEmpty(tasks) && tasks.stream().allMatch(e -> e.getStatus() == 2);
            if (success) {
                Content content = contentService.queryById(Convert.toLong(taskId));
                VideoCombineResult result = new VideoCombineResult();
                result.setVideoPath(content.getMediaUrl());
                result.setDuration(content.getDuration());
                result.setTaskId(taskId);
                result.setCoverImage(content.getCoverImg());
                result.setDone(true);
                result.setResult("1");
                return result;
            }
            //全部进行中
            VideoCombineResult result = new VideoCombineResult();
            result.setDone(false);
            return result;
        }).map(resp -> {
            return new SimpleResponseBuilder<>(resp).success().bulider();
        });
    }

    /**
     * 更新剪辑视频
     */
    @PostMapping("/video/clip/update")
    public SimpleResponse<String> updateClipVideo(@RequestBody ContentDTO contentDTO) {
        Content content = new Content();
        content.setId(contentDTO.getId());
        content.setExposeMediaUrl(contentDTO.getExposeMediaUrl());
        //生成封面
        File coverImageFile = MediaUtil.getVideoCover(contentDTO.getExposeMediaUrl());
        String coverImage = ossUtil.upload(coverImageFile);
        List<String> imgs = Collections.singletonList(coverImage);
        content.setCoverImg(JSONObject.toJSONString(imgs));
        content.setDuration(MediaUtil.getDuration(contentDTO.getExposeMediaUrl()));
        content.setStatus(contentDTO.getStatus());
        contentService.update(content);
        return new SimpleResponseBuilder<>("").success().bulider();
    }

}
