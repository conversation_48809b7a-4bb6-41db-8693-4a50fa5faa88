package com.lazhu.mongo.entity;

import lombok.Data;

/**
 * 热点话题查询参数
 */
@Data
public class HotTopicsQuery {
    
    /**
     * 页码，默认1
     */
    private Integer page = 1;
    
    /**
     * 每页大小，默认20
     */
    private Integer size = 20;
    
    /**
     * 数据来源（可选）
     */
    private String platSource;
    
    /**
     * 热点日期（可选，格式：yyyy-MM-dd）
     */
    private String topicDate;
    
    /**
     * 关键字（可选）
     */
    private String keyword;
}
