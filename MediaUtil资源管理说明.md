# MediaUtil 资源管理说明

## 概述

在重构 `MediaUtil` 时，我们添加了完整的资源管理功能，确保所有系统资源（进程、IO流、临时文件）都能得到正确的清理，避免资源泄漏。

## 资源管理架构

### 1. 进程资源管理

#### 问题
FFmpeg 执行会创建子进程，如果不正确清理可能导致：
- 进程僵尸化
- 文件描述符泄漏
- 内存泄漏

#### 解决方案
```java
private static void cleanupResources(Process process, BufferedReader reader) {
    // 1. 关闭IO流
    if (reader != null) {
        try {
            reader.close();
        } catch (Exception e) {
            log.warn("关闭BufferedReader失败", e);
        }
    }

    // 2. 清理进程资源
    if (process != null) {
        try {
            // 关闭进程的所有流
            if (process.getInputStream() != null) {
                process.getInputStream().close();
            }
            if (process.getOutputStream() != null) {
                process.getOutputStream().close();
            }
            if (process.getErrorStream() != null) {
                process.getErrorStream().close();
            }
            
            // 3. 优雅终止进程
            if (process.isAlive()) {
                process.destroy();
                
                // 4. 强制终止（如果需要）
                if (!process.waitFor(5, TimeUnit.SECONDS)) {
                    process.destroyForcibly();
                }
            }
        } catch (Exception e) {
            log.warn("清理进程资源失败", e);
        }
    }
}
```

### 2. 临时文件管理

#### 问题
FFmpeg 操作会产生大量临时文件：
- 视频封面图片
- 截取的视频片段
- 下载的网络视频
- 合并用的列表文件

#### 解决方案

##### 临时文件跟踪
```java
// 全局临时文件列表
private static final List<File> TEMP_FILES = new ArrayList<>();

// 创建临时文件并加入跟踪
private static File createTempFile(String prefix, String suffix) {
    File file = new File(tempDir, prefix + "_" + System.currentTimeMillis() + suffix);
    synchronized (TEMP_FILES) {
        TEMP_FILES.add(file);
    }
    return file;
}
```

##### 手动清理
```java
// 清理单个文件
public static void cleanupTempFile(File file) {
    if (file != null && file.exists()) {
        FileUtil.del(file);
        synchronized (TEMP_FILES) {
            TEMP_FILES.remove(file);
        }
    }
}

// 清理所有临时文件
public static void cleanupAllTempFiles() {
    synchronized (TEMP_FILES) {
        for (File file : new ArrayList<>(TEMP_FILES)) {
            cleanupTempFile(file);
        }
        TEMP_FILES.clear();
    }
}
```

##### JVM 关闭钩子
```java
static {
    // 注册关闭钩子，确保程序退出时清理资源
    Runtime.getRuntime().addShutdownHook(new Thread(() -> {
        log.info("JVM关闭，开始清理MediaUtil资源...");
        cleanupAllTempFiles();
        log.info("MediaUtil资源清理完成");
    }, "MediaUtil-Cleanup-Thread"));
}
```

## 资源管理策略

### 1. 自动管理 vs 手动管理

#### 自动管理（推荐）
适用于大多数场景，文件会自动加入跟踪列表：
```java
// 自动管理的临时文件
File coverFile = createTempFile("video_cover", ".jpg");
// 程序退出时自动清理
```

#### 手动管理
适用于需要精确控制生命周期的场景：
```java
// 手动管理的临时文件
File tempFile = createTempFileUnmanaged("video_merge", ".mp4");
try {
    // 使用文件
    processVideo(tempFile);
} finally {
    // 手动清理
    cleanupTempFile(tempFile);
}
```

### 2. 资源清理时机

#### 立即清理
```java
public static String cut(String url, long start, long end) {
    File outputFile = createTempFile("cut", ".mp4");
    
    FFmpegResult result = executeFFmpegCommand(args, outputFile);
    
    if (result.isSuccess()) {
        return outputFile.getAbsolutePath();
    } else {
        // 失败时立即清理
        cleanupTempFile(outputFile);
        return null;
    }
}
```

#### 延迟清理
```java
public static File getVideoCover(String url) {
    File coverFile = createTempFile("cover", ".jpg");
    
    FFmpegResult result = executeFFmpegCommand(args, coverFile);
    
    // 成功时返回文件，由调用者决定何时清理
    // 或者依赖JVM关闭钩子清理
    return result.isSuccess() ? coverFile : null;
}
```

#### 批量清理
```java
public static void merge(List<String> urls, String outPath) {
    List<File> tempFiles = new ArrayList<>();
    
    try {
        // 创建多个临时文件
        for (String url : urls) {
            File tempFile = createTempFileUnmanaged("merge", ".mp4");
            tempFiles.add(tempFile);
            // 处理文件...
        }
        
        // 执行合并操作
        
    } finally {
        // 批量清理
        for (File tempFile : tempFiles) {
            cleanupTempFile(tempFile);
        }
    }
}
```

## 最佳实践

### 1. 使用 try-finally 确保清理

```java
public void processVideo() {
    File tempFile = null;
    try {
        tempFile = createTempFile("process", ".mp4");
        // 处理逻辑
    } finally {
        if (tempFile != null) {
            cleanupTempFile(tempFile);
        }
    }
}
```

### 2. 使用 try-with-resources 管理IO

```java
private static FFmpegResult executeFFmpegCommand(String[] args, File outputFile, Consumer<String> progressCallback) {
    Process process = null;
    
    try {
        process = new ProcessBuilder(args).start();
        
        // 使用 try-with-resources 自动关闭流
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
            // 读取输出
        }
        
        return new FFmpegResult(process.waitFor(), output, outputFile);
        
    } finally {
        cleanupResources(process, null);
    }
}
```

### 3. 监控资源使用

```java
// 获取当前临时文件数量
int tempFileCount = MediaUtil.getTempFileCount();
log.info("当前临时文件数量: {}", tempFileCount);

// 定期清理（可选）
if (tempFileCount > 100) {
    MediaUtil.cleanupAllTempFiles();
}
```

### 4. 异常情况下的资源清理

```java
public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath, String fontsDir, Integer quality) {
    File outputFile = new File(outPath);
    
    try {
        // 执行FFmpeg命令
        FFmpegResult result = executeFFmpegCommand(args, outputFile);
        
        if (!result.isSuccess()) {
            // 失败时清理可能产生的不完整文件
            if (outputFile.exists()) {
                cleanupTempFile(outputFile);
            }
            throw new RuntimeException("合成ASS字幕失败");
        }
    } catch (Exception e) {
        // 异常时也要清理资源
        if (outputFile.exists()) {
            cleanupTempFile(outputFile);
        }
        throw e;
    }
}
```

## 资源清理检查清单

### 开发时检查
- [ ] 每个创建临时文件的地方都有对应的清理逻辑
- [ ] 使用 try-finally 或 try-with-resources 确保资源清理
- [ ] 异常情况下也能正确清理资源
- [ ] 长时间运行的应用定期清理临时文件

### 测试时检查
- [ ] 验证临时文件在操作完成后被正确删除
- [ ] 验证进程在操作完成后正确终止
- [ ] 验证异常情况下资源仍能正确清理
- [ ] 验证JVM关闭时资源能正确清理

### 生产环境监控
- [ ] 监控临时目录的磁盘使用情况
- [ ] 监控进程数量，确保没有僵尸进程
- [ ] 监控文件描述符使用情况
- [ ] 设置定期清理任务（如果需要）

## 总结

通过完整的资源管理机制，我们确保了：

1. **进程资源**：所有FFmpeg进程都能正确终止，避免僵尸进程
2. **IO资源**：所有输入输出流都能正确关闭，避免文件描述符泄漏
3. **文件资源**：所有临时文件都能被跟踪和清理，避免磁盘空间浪费
4. **异常安全**：即使在异常情况下也能保证资源清理
5. **自动清理**：JVM关闭时自动清理所有资源，确保系统整洁

这些改进使得 `MediaUtil` 更加健壮和可靠，适合在生产环境中长时间运行。
