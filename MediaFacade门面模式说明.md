# MediaFacade 门面模式说明

## 概述

基于门面模式（Facade Pattern）创建的 `MediaFacade` 类，为所有媒体处理功能提供了一个统一的入口点。这种设计简化了客户端的调用方式，隐藏了底层服务类的复杂性。

## 门面模式的优势

### 1. 统一的接口
- 所有媒体处理功能都通过 `MediaFacade` 一个类访问
- 客户端无需了解底层的多个服务类
- 提供一致的API设计和调用方式

### 2. 简化的调用
```java
// 使用门面模式前（需要了解多个服务类）
VideoInfoService.getDuration("video.mp4");
VideoProcessService.cut("video.mp4", 10, 30);
SubtitleService.mergeAssSubtitle("video.mp4", "sub.ass", "out.mp4");
MediaResourceManager.cleanupAllTempFiles();

// 使用门面模式后（统一入口）
MediaFacade.getDuration("video.mp4");
MediaFacade.cut("video.mp4", 10, 30);
MediaFacade.mergeAssSubtitle("video.mp4", "sub.ass", "out.mp4");
MediaFacade.cleanupAllTempFiles();
```

### 3. 高级组合功能
门面类不仅提供基础功能的封装，还提供了高级的组合功能：

```java
// 快速处理：截取 + 添加字幕 + 转换格式
ProcessResult result = MediaFacade.quickProcess(
    "input.mp4", 30, 120, "subtitle.ass", "output.mp4", 23);

// 批量处理：为多个视频添加相同字幕
BatchProcessResult batchResult = MediaFacade.batchAddSubtitle(
    videoList, "subtitle.ass", "outputDir");

// 智能压缩：根据目标大小自动调整质量
CompressResult compressResult = MediaFacade.smartCompress(
    "input.mp4", "output.mp4", 50.0, 5);

// 视频预览：生成封面和多个截图
PreviewResult previewResult = MediaFacade.createPreview(
    "video.mp4", Arrays.asList(10L, 30L, 60L));
```

## 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    MediaFacade 门面层                        │
├─────────────────────────────────────────────────────────────┤
│ • 统一接口                                                   │
│ • 组合功能                                                   │
│ • 结果封装                                                   │
│ • 错误处理                                                   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    底层服务类                                │
├─────────────────────────────────────────────────────────────┤
│ VideoInfoService    │ VideoProcessService │ SubtitleService │
│ MediaResourceManager│ FFmpegExecutor      │ MediaPathUtils  │
└─────────────────────────────────────────────────────────────┘
```

## 功能分类

### 1. 视频信息功能
```java
// 基础信息
Long duration = MediaFacade.getDuration(url);
VideoInfoService.VideoInfo info = MediaFacade.getVideoInfo(url);

// 图片生成
File cover = MediaFacade.getVideoCover(url);
File screenshot = MediaFacade.getVideoScreenshot(url, timeSeconds);
```

### 2. 视频处理功能
```java
// 基础处理
String cutResult = MediaFacade.cut(url, start, end);
MediaFacade.merge(videoList, outputPath);

// 格式转换
MediaFacade.convert(inputPath, outputPath, videoCodec, audioCodec, quality);
MediaFacade.resize(inputPath, outputPath, width, height);
```

### 3. 字幕处理功能
```java
// 基础字幕
MediaFacade.addSubtitle(videoUrl, subtitlePath, outPath);

// ASS字幕
MediaFacade.mergeAssSubtitle(videoUrl, assPath, outPath, fontsDir, quality);
MediaFacade.addAssSoftSubtitle(videoUrl, assPath, outPath, language, title);

// 批量字幕
MediaFacade.addMultipleSubtitles(videoUrl, subtitleInfos, outPath);
```

### 4. 资源管理功能
```java
// 文件管理
File tempFile = MediaFacade.createTempFile(prefix, suffix);
MediaFacade.cleanupTempFile(file);
MediaFacade.cleanupAllTempFiles();

// 监控统计
int count = MediaFacade.getTempFileCount();
MediaResourceManager.TempFileStats stats = MediaFacade.getTempFileStats();
MediaFacade.performMaintenanceCleanup();
```

### 5. 路径处理功能
```java
// 路径处理
String ffmpegPath = MediaFacade.processPathForFFmpeg(path);
String safePath = MediaFacade.buildSafeOutputPath(dir, fileName);

// 系统信息
boolean isWindows = MediaFacade.isWindows();
String fontsDir = MediaFacade.getDefaultFontsDir();
```

### 6. FFmpeg执行功能
```java
// 命令构建和执行
FFmpegExecutor.FFmpegCommandBuilder builder = MediaFacade.createCommandBuilder();
FFmpegExecutor.FFmpegResult result = MediaFacade.executeCommand(args, outputFile);
```

## 高级组合功能

### 1. 快速处理 (quickProcess)
**功能**：一次性完成视频截取、字幕添加和格式转换
```java
ProcessResult result = MediaFacade.quickProcess(
    "input.mp4",     // 输入视频
    30,              // 开始时间（秒）
    120,             // 结束时间（秒）
    "subtitle.ass",  // 字幕文件（可选）
    "output.mp4",    // 输出文件
    23               // 质量参数（可选）
);
```

**处理流程**：
1. 截取指定时间段的视频
2. 如果提供字幕文件，添加字幕
3. 转换为指定格式和质量
4. 返回详细的处理结果

### 2. 批量处理 (batchAddSubtitle)
**功能**：为多个视频批量添加相同的字幕
```java
BatchProcessResult result = MediaFacade.batchAddSubtitle(
    Arrays.asList("video1.mp4", "video2.mp4", "video3.mp4"),
    "common_subtitle.ass",
    "output_directory"
);
```

**特点**：
- 支持批量处理多个视频
- 统计成功和失败的数量
- 提供详细的处理报告
- 自动错误恢复和继续处理

### 3. 智能压缩 (smartCompress)
**功能**：根据目标文件大小自动调整压缩参数
```java
CompressResult result = MediaFacade.smartCompress(
    "large_video.mp4",  // 输入文件
    "compressed.mp4",   // 输出文件
    50.0,               // 目标大小（MB）
    5                   // 最大尝试次数
);
```

**智能算法**：
1. 分析原始文件大小
2. 计算初始压缩参数
3. 迭代调整质量参数
4. 直到达到目标大小或最大尝试次数

### 4. 视频预览 (createPreview)
**功能**：生成视频封面和多个时间点的截图
```java
PreviewResult result = MediaFacade.createPreview(
    "video.mp4",
    Arrays.asList(10L, 30L, 60L, 90L, 120L)  // 截图时间点
);
```

**生成内容**：
- 视频第一帧作为封面
- 指定时间点的截图
- 详细的视频信息
- 生成统计信息

## 结果封装

### 1. ProcessResult - 处理结果
```java
public class ProcessResult {
    boolean isSuccess()                    // 是否成功
    String getMessage()                    // 成功消息
    String getErrorMessage()               // 错误消息
    String getOutputFile()                 // 输出文件路径
    String getFormattedFileSize()          // 格式化文件大小
    String getFormattedProcessingTime()    // 格式化处理时间
}
```

### 2. BatchProcessResult - 批量处理结果
```java
public class BatchProcessResult {
    int getTotalCount()                    // 总处理数量
    int getSuccessCount()                  // 成功数量
    int getFailureCount()                  // 失败数量
    double getSuccessRate()                // 成功率
    String getDetailedStats()              // 详细统计
    List<String> getSuccessOutputFiles()   // 成功的输出文件
}
```

### 3. CompressResult - 压缩结果
```java
public class CompressResult {
    double getCompressionRate()            // 压缩率
    double getSavedSizeMB()                // 节省的空间
    boolean isTargetAchieved()             // 是否达到目标
    String getCompressionEffectiveness()   // 压缩效果评估
    String getDetailedReport()             // 详细报告
}
```

### 4. PreviewResult - 预览结果
```java
public class PreviewResult {
    VideoInfoService.VideoInfo getVideoInfo()  // 视频信息
    File getCoverFile()                         // 封面文件
    Map<Long, File> getScreenshots()            // 截图文件映射
    String getSummary()                         // 预览摘要
    String getFormattedTotalFileSize()          // 总文件大小
}
```

## 使用建议

### 1. 简单操作使用基础方法
```java
// 获取视频信息
Long duration = MediaFacade.getDuration("video.mp4");
File cover = MediaFacade.getVideoCover("video.mp4");

// 基础处理
String cutResult = MediaFacade.cut("video.mp4", 10, 30);
MediaFacade.mergeAssSubtitle("video.mp4", "sub.ass", "out.mp4");
```

### 2. 复杂操作使用组合方法
```java
// 一站式处理
ProcessResult result = MediaFacade.quickProcess(
    "input.mp4", 30, 120, "subtitle.ass", "output.mp4", 23);

// 批量操作
BatchProcessResult batchResult = MediaFacade.batchAddSubtitle(
    videoList, "subtitle.ass", "outputDir");
```

### 3. 资源管理
```java
// 定期清理
MediaFacade.performMaintenanceCleanup();

// 监控使用
if (MediaFacade.getTempFileCount() > 10) {
    MediaFacade.cleanupAllTempFiles();
}
```

### 4. 错误处理
```java
try {
    ProcessResult result = MediaFacade.quickProcess(...);
    if (result.isSuccess()) {
        log.info("处理成功: {}", result.getOutputFile());
    } else {
        log.error("处理失败: {}", result.getErrorMessage());
    }
} catch (Exception e) {
    log.error("处理异常", e);
}
```

## 总结

`MediaFacade` 门面类的设计带来了以下好处：

1. **简化接口**：统一的入口点，降低学习成本
2. **隐藏复杂性**：客户端无需了解底层实现细节
3. **组合功能**：提供高级的一站式处理能力
4. **一致性**：统一的错误处理和结果封装
5. **可维护性**：底层变化不影响客户端代码
6. **扩展性**：易于添加新的组合功能

通过门面模式，我们既保持了底层服务类的专业性和灵活性，又为客户端提供了简单易用的接口，实现了复杂性的有效管理。
