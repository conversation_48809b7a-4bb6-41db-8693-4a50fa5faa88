# ASS字幕合成功能使用说明

## 概述

在 `MediaUtil.java` 中新增了专门用于处理 ASS 字幕的方法，支持硬字幕烧录和软字幕嵌入两种方式。

## 新增方法

### 1. 硬字幕烧录方法

#### `mergeAssSubtitle(String videoUrl, String assPath, String outPath, String fontsDir, Integer quality)`

**功能**: 将ASS字幕永久烧录到视频中（硬字幕）

**参数**:
- `videoUrl`: 视频路径（支持本地文件和网络URL）
- `assPath`: ASS字幕文件路径
- `outPath`: 输出文件路径
- `fontsDir`: 字体目录路径（可选，为null时使用系统默认）
- `quality`: 视频质量CRF值（18-28，数值越小质量越高，可选，默认23）

**示例**:
```java
// 基本用法
MediaUtil.mergeAssSubtitle("input.mp4", "subtitle.ass", "output.mp4");

// 高质量输出
MediaUtil.mergeAssSubtitle("input.mp4", "subtitle.ass", "output.mp4", null, 18);

// 指定字体目录
MediaUtil.mergeAssSubtitle("input.mp4", "subtitle.ass", "output.mp4", "C:/Windows/Fonts", 23);
```

#### `mergeAssSubtitle(String videoUrl, String assPath, String outPath)`

**功能**: 使用默认参数的硬字幕烧录

**示例**:
```java
MediaUtil.mergeAssSubtitle("input.mp4", "subtitle.ass", "output.mp4");
```

### 2. 软字幕嵌入方法

#### `addAssSoftSubtitle(String videoUrl, String assPath, String outPath, String language, String title)`

**功能**: 将ASS字幕作为独立流嵌入视频（软字幕）

**参数**:
- `videoUrl`: 视频路径
- `assPath`: ASS字幕文件路径
- `outPath`: 输出文件路径（建议使用.mkv格式）
- `language`: 字幕语言标识（可选，如"chi"、"eng"等）
- `title`: 字幕标题（可选，如"中文"、"English"等）

**示例**:
```java
// 添加中文软字幕
MediaUtil.addAssSoftSubtitle("input.mp4", "subtitle.ass", "output.mkv", "chi", "中文字幕");

// 添加英文软字幕
MediaUtil.addAssSoftSubtitle("input.mp4", "subtitle.ass", "output.mkv", "eng", "English Subtitle");
```

#### `addAssSoftSubtitle(String videoUrl, String assPath, String outPath)`

**功能**: 使用默认参数的软字幕嵌入

**示例**:
```java
MediaUtil.addAssSoftSubtitle("input.mp4", "subtitle.ass", "output.mkv");
```

## 硬字幕 vs 软字幕

### 硬字幕（推荐用于最终发布）

**优点**:
- 兼容性好，所有播放器都能显示
- 字幕样式完全保留
- 不依赖播放器的字幕渲染能力

**缺点**:
- 文件体积较大
- 无法关闭字幕
- 处理时间较长

**适用场景**:
- 最终发布的视频
- 需要确保字幕显示效果的场合
- 目标播放器不支持ASS字幕的情况

### 软字幕（推荐用于保留控制权）

**优点**:
- 文件体积小
- 可以开关字幕
- 处理速度快
- 支持多语言字幕

**缺点**:
- 需要播放器支持ASS格式
- 字体依赖播放设备
- 部分复杂效果可能不支持

**适用场景**:
- 需要多语言支持
- 用户可能需要关闭字幕
- 中间处理步骤

## 容器格式支持

| 格式 | 硬字幕 | 软字幕 | 推荐用途 |
|------|--------|--------|----------|
| MP4  | ✅     | ⚠️     | 硬字幕输出 |
| MKV  | ✅     | ✅     | 软字幕输出 |
| AVI  | ✅     | ❌     | 硬字幕输出 |
| MOV  | ✅     | ⚠️     | 硬字幕输出 |

注：⚠️ 表示有限支持，可能存在兼容性问题

## 质量参数说明

CRF（Constant Rate Factor）质量参数：

- **18**: 视觉无损质量（文件很大）
- **23**: 默认质量（推荐）
- **28**: 较低质量（文件较小）

## 字体目录配置

### Windows
```java
String fontsDir = "C:/Windows/Fonts";
```

### Linux
```java
String fontsDir = "/usr/share/fonts";
```

### macOS
```java
String fontsDir = "/System/Library/Fonts";
```

## 路径处理

工具类会自动处理以下路径问题：
- Windows路径的反斜杠转换
- 特殊字符转义
- 空格和括号处理
- 盘符格式转换

## 错误处理

所有方法都包含完整的错误处理：
- 参数验证
- 文件存在性检查
- FFmpeg执行状态监控
- 输出文件验证
- 详细的日志记录

## 性能优化建议

1. **硬字幕烧录**:
   - 使用适当的CRF值（推荐23）
   - 避免过高的质量设置
   - 考虑使用硬件加速（需要额外配置）

2. **软字幕嵌入**:
   - 优先选择MKV容器
   - 批量处理时复用字幕文件
   - 避免频繁的格式转换

3. **批量处理**:
   - 使用线程池并行处理
   - 监控系统资源使用
   - 实现进度回调机制

## 示例代码

完整的使用示例请参考 `AssSubtitleExample.java` 文件，包含：
- 基本硬字幕烧录
- 软字幕嵌入
- 高质量输出
- 自定义字体
- 批量处理
- 多语言字幕

## 注意事项

1. 确保ASS字幕文件格式正确
2. 检查字体文件是否存在（硬字幕）
3. 选择合适的输出容器格式
4. 监控处理进度和错误信息
5. 验证输出文件的完整性
