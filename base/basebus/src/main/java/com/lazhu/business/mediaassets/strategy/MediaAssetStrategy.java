package com.lazhu.business.mediaassets.strategy;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.mediaassets.dto.MediaAssetsDTO;
import com.lazhu.business.mediaassets.dto.MediaAssetsQuery;

import java.util.List;

/**
 * 素材操作策略接口
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
public interface MediaAssetStrategy {

    /**
     * 查询素材列表
     *
     * @param query 查询条件
     * @return 素材分页列表
     */
    Page<MediaAssetsDTO> queryAssets(MediaAssetsQuery query);


    Long countAssets(MediaAssetsQuery query);

    /**
     * 上传素材
     * @return 上传结果
     */
    MediaAssetsDTO uploadAsset(MediaAssetsDTO param);

    /**
     * 更新素材
     *
     * @param id 素材ID
     * @param title 标题
     * @return 更新结果
     */
    Boolean updateAsset(Long id, String title);


    /**
     * 批量删除素材
     *
     * @param ids 素材ID列表
     * @return 删除结果
     */
    Boolean batchDeleteAssets(List<Long> ids);

}