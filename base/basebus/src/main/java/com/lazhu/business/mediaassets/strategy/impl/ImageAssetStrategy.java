package com.lazhu.business.mediaassets.strategy.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.mediaassets.dto.MediaAssetsDTO;
import com.lazhu.business.mediaassets.dto.MediaAssetsQuery;
import com.lazhu.business.mediaassets.entity.MediaAssetsImg;
import com.lazhu.business.mediaassets.service.MediaAssetsImgService;
import com.lazhu.business.mediaassets.strategy.MediaAssetStrategy;
import com.lazhu.common.enums.MediaAssetSourceEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 图片素材策略实现
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Component
public class ImageAssetStrategy implements MediaAssetStrategy {

    @Autowired
    private MediaAssetsImgService mediaAssetsImgService;

    @Override
    public Page<MediaAssetsDTO> queryAssets(MediaAssetsQuery query) {
        // 执行查询
        Page<MediaAssetsImg> imgPage = mediaAssetsImgService.queryPage(BeanUtil.beanToMap(query));

        // 转换为统一素材对象
        List<MediaAssetsDTO> unifiedAssets = imgPage.getRecords().stream().map(img ->
                new MediaAssetsDTO(
                        img.getId(),
                        "1", // 图片类型
                        MediaAssetSourceEnum.SYSTEM.getSource(),
                        img.getTitle(),
                        img.getImgUrl(),
                        null,
                        null,
                        img.getCreateTime()
                )
        ).collect(Collectors.toList());

        // 创建返回的分页对象
        Page<MediaAssetsDTO> resultPage = new Page<>(imgPage.getCurrent(), imgPage.getSize());
        resultPage.setRecords(unifiedAssets);
        resultPage.setTotal(imgPage.getTotal());

        return resultPage;
    }

    @Override
    public Long countAssets(MediaAssetsQuery query) {
        return mediaAssetsImgService.count(BeanUtil.beanToMap(query));
    }


    @Override
    public MediaAssetsDTO uploadAsset(MediaAssetsDTO param) {
        MediaAssetsImg img = new MediaAssetsImg();
        img.setTitle(param.getTitle());
        img.setImgUrl(param.getUrl());

        mediaAssetsImgService.save(img);

        return new MediaAssetsDTO(
                img.getId(),
                "1", // 图片类型
                MediaAssetSourceEnum.SYSTEM.getSource(),
                img.getTitle(),
                img.getImgUrl(),
                null,
                null,
                img.getCreateTime()
        );
    }

    @Override
    public Boolean updateAsset(Long id, String title) {
        MediaAssetsImg img = new MediaAssetsImg();
        img.setId(id);
        img.setTitle(title);
        mediaAssetsImgService.update(img);
        return true;
    }

    @Override
    public Boolean batchDeleteAssets(List<Long> ids) {
        return mediaAssetsImgService.batchDelById(ids) > 0;
    }
}