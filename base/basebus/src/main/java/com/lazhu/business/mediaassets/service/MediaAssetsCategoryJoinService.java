package com.lazhu.business.mediaassets.service;

import cn.hutool.core.collection.CollUtil;
import com.lazhu.business.mediaassets.entity.MediaAssetsCategoryJoin;
import com.lazhu.business.mediaassets.mapper.MediaAssetsCategoryRelMapper;
import com.lazhu.support.base.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 素材分类关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Service
public class MediaAssetsCategoryJoinService extends BaseService<MediaAssetsCategoryJoin> {

    @Autowired
    private MediaAssetsCategoryRelMapper baseMapper;

    @Transactional
    public void addAssetCategories(List<Long> assetIds, String assetType, List<Long> categoryIds, Integer source) {
        List<MediaAssetsCategoryJoin> rels = new ArrayList<>();
        for (Long categoryId : categoryIds) {
            MediaAssetsCategoryJoin rel = new MediaAssetsCategoryJoin();
            for (Long assetId : assetIds) {
                rel.setAssetId(assetId);
                rel.setAssetType(assetType);
                rel.setCategoryId(categoryId);
                rel.setSource(source);
                rels.add(rel);
            }
        }
        baseMapper.insert(rels);
    }



    @Transactional
    public void setAssetCategories(Long assetId, String assetType, List<Long> categoryIds, Integer source) {
        if (CollUtil.isEmpty(categoryIds)) {
            return;
        }
        // 先删除现有关联
        baseMapper.deleteByAsset(Collections.singletonList(assetId), assetType, source);
        // 添加新关联
        addAssetCategories(Collections.singletonList(assetId), assetType, categoryIds, source);
    }

    @Transactional
    public void setAssetCategories(List<Long> assetIds, String assetType, List<Long> categoryIds, Integer source) {
        if (CollUtil.isEmpty(categoryIds)) {
            return;
        }
        // 先删除现有关联
        baseMapper.deleteByAsset(assetIds, assetType, source);
        // 添加新关联
        addAssetCategories(assetIds, assetType, categoryIds, source);
    }

    /**
     * 查询指定分类下的素材ID列表
     *
     * @param categoryId 分类ID
     * @param assetType  素材类型
     * @param source     素材来源
     * @return 素材ID列表
     */
    public List<Long> queryCategoryAssetIds(Long categoryId, String assetType, Integer source) {
        List<MediaAssetsCategoryJoin> rels = baseMapper.queryByCategory(categoryId, assetType, source);
        return rels.stream().map(MediaAssetsCategoryJoin::getAssetId).collect(Collectors.toList());
    }


    @Transactional
    public void deleteByAsset(List<Long> assetIds, String assetType, Integer source) {
        baseMapper.deleteByAsset(assetIds, assetType, source);
    }

}