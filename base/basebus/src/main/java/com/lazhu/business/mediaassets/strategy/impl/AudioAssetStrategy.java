package com.lazhu.business.mediaassets.strategy.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.mediaassets.dto.MediaAssetsDTO;
import com.lazhu.business.mediaassets.dto.MediaAssetsQuery;
import com.lazhu.business.mediaassets.entity.MediaAssetsVoice;
import com.lazhu.business.mediaassets.service.MediaAssetsVoiceService;
import com.lazhu.business.mediaassets.strategy.MediaAssetStrategy;
import com.lazhu.common.enums.MediaAssetSourceEnum;
import com.lazhu.common.media.MediaUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 音频素材策略实现
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Component
public class AudioAssetStrategy implements MediaAssetStrategy {

    @Autowired
    private MediaAssetsVoiceService mediaAssetsVoiceService;

    @Override
    public Page<MediaAssetsDTO> queryAssets(MediaAssetsQuery query) {
        // 执行查询
        Page<MediaAssetsVoice> voicePage = mediaAssetsVoiceService.queryPage(BeanUtil.beanToMap(query));

        // 转换为统一素材对象
        List<MediaAssetsDTO> unifiedAssets = voicePage.getRecords().stream().map(voice ->
                new MediaAssetsDTO(
                        voice.getId(),
                        "3", // 音频类型
                        MediaAssetSourceEnum.SYSTEM.getSource(),
                        voice.getTitle(),
                        voice.getAudioUrl(),
                        voice.getDurationMs(),
                        null,
                        voice.getCreateTime()
                )
        ).collect(Collectors.toList());

        // 创建返回的分页对象
        Page<MediaAssetsDTO> resultPage = new Page<>(voicePage.getCurrent(), voicePage.getSize());
        resultPage.setRecords(unifiedAssets);
        resultPage.setTotal(voicePage.getTotal());

        return resultPage;
    }

    @Override
    public Long countAssets(MediaAssetsQuery query) {
        return mediaAssetsVoiceService.count(BeanUtil.beanToMap(query));
    }


    @Override
    public MediaAssetsDTO uploadAsset(MediaAssetsDTO param) {
        MediaAssetsVoice voice = new MediaAssetsVoice();
        voice.setTitle(param.getTitle());
        voice.setAudioUrl(param.getUrl());
        Long duration = MediaUtil.getDuration(param.getUrl());
        voice.setDurationMs(duration);

        mediaAssetsVoiceService.save(voice);
        return new MediaAssetsDTO(
                voice.getId(),
                "3", // 音频类型
                MediaAssetSourceEnum.SYSTEM.getSource(),
                voice.getTitle(),
                voice.getAudioUrl(),
                voice.getDurationMs(),
                null,
                voice.getCreateTime()
        );
    }

    @Override
    public Boolean updateAsset(Long id, String title) {
        MediaAssetsVoice voice = new MediaAssetsVoice();
        voice.setId(id);
        voice.setTitle(title);
        mediaAssetsVoiceService.update(voice);
        return true;
    }

    @Override
    public Boolean batchDeleteAssets(List<Long> ids) {
        return mediaAssetsVoiceService.batchDelById(ids) > 0;
    }

}