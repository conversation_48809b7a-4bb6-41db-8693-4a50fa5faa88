package com.lazhu.business.content.service;

import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.alibaba.fastjson.JSONObject;
import com.lazhu.baseai.llm.dto.TextCreationReq;
import cn.hutool.core.util.StrUtil;
import com.lazhu.business.actor.entity.Actor;
import com.lazhu.business.actor.service.ActorService;
import com.lazhu.business.actorconfig.entity.ActorConfig;
import com.lazhu.business.actorconfig.service.ActorConfigService;
import com.lazhu.business.actorcontentexample.entity.ActorContentExample;
import com.lazhu.business.actorcontentexample.service.ActorContentExampleService;
import com.lazhu.business.content.dto.ContentCreationReq;
import com.lazhu.common.enums.ContentTypeEnum;
import com.lazhu.system.dic.entity.Dic;
import com.lazhu.system.dic.service.DicService;
import com.lazhu.system.sysparams.service.SysParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.lazhu.support.base.BaseService;
import com.lazhu.business.content.entity.Content;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 内容管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class ContentService extends BaseService<Content> {


    @Autowired
    private SysParamsService sysParamsService;

    @Autowired
    private ActorService actorService;

    @Autowired
    private ActorConfigService actorConfigService;

    @Autowired
    private DicService dicService;

    @Autowired
    private ActorContentExampleService actorContentExampleService;


    /**
     * 将ContentCreationReq.TextRequest转换为TextCreationReq对象
     *
     * @param request ContentCreationReq.TextRequest对象
     * @return TextCreationReq对象
     */
    public TextCreationReq fromTextCreateRequest(ContentCreationReq.TextRequest request) {
        TextCreationReq textCreationReq = new TextCreationReq();

        String prompt = combinePrompt(request);
        textCreationReq.setPrompt(prompt);
        // 设置内容类型
        textCreationReq.setContentType(request.getContentType());
        textCreationReq.setPlat(request.getMetaData().getPlat());
        // 编辑索引默认为null（第一次生成为空）
        textCreationReq.setEditIndex(null);
        // 创建文本时默认为"开始生成"
        textCreationReq.setQuery("开始生成");
        textCreationReq.setIdea(request.getTopic());
        //fill example content
        ActorContentExample example = actorContentExampleService.queryByActorIdAndPlat(request.getActorId(), request.getMetaData().getPlat());
        if (example != null) {
            textCreationReq.setExample(example.getContent());
        }
        return textCreationReq;
    }

    public TextCreationReq TextFineTuneRequest(ContentCreationReq.TextFineTuneRequest request) {
        TextCreationReq textCreationReq = fromTextCreateRequest(request.textRequest());
        textCreationReq.setQuery(request.query());
        textCreationReq.setEditIndex(request.editIndex());
        textCreationReq.setTitle(request.title());
        textCreationReq.setContent(request.content());
        return textCreationReq;
    }

    public String combinePrompt(ContentCreationReq.TextRequest request) {
        // 查询提示词模板
        String promptTpl;
        if (request.getContentType().equals(ContentTypeEnum.TXT.getType())) {
            // 图文
            promptTpl = sysParamsService.queryByKey("txt_create_prompt");
        } else {
            // 视频
            promptTpl = sysParamsService.queryByKey("video_txt_create_prompt");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("plat", request.getMetaData().getPlat());
        Actor actor = actorService.queryById(request.getActorId());
        params.put("author_prompt", actor.getRolePrompt());
        params.put("theme", request.getTopic());
        params.put("user_role", actor.getNickName());
        params.put("word_count_max", request.getMetaData().getContentLength());
        params.put("other_requirements", request.getMetaData().getOtherDesc());

        //设置创作要求
        List<ActorConfig> list = actorConfigService.queryByIds(request.getStyleIds());
        //根据类型分组
        Map<String, List<ActorConfig>> styleGroup = list.stream().collect(Collectors.groupingBy(ActorConfig::getCharacterType));
        //查询字典：
        List<Dic> chacterType = dicService.queryByCodes(Collections.singletonList("CHACTER_TYPE"));
        //转map
        Map<String, Dic> chacterMap = chacterType.stream().collect(Collectors.toMap(Dic::getValue, dic -> dic));
        StringBuilder sb = new StringBuilder();
        int idx = 1;
        for (String key : styleGroup.keySet()) {
            String keyName = chacterMap.get(key).getTxt();
            StringBuilder sb1 = new StringBuilder();
            for (ActorConfig config : styleGroup.get(key)) {
                sb1.append(" - ").append(config.getCharacterName()).append(" : ").append(config.getCharacterPrompt()).append(" \n ");
            }
            sb.append(idx).append(". ").append("**").append(keyName).append("** : \n ").append(sb1).append(" \n ");
            idx++;
        }
        params.put("styles", sb.toString());
        //fill example content
        ActorContentExample example = actorContentExampleService.queryByActorIdAndPlat(request.getActorId(), request.getMetaData().getPlat());
        if (example != null) {
            params.put("example_title", example.getTitle());
            params.put("example_content", example.getContent());
        }
        TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig());
        Template template = engine.getTemplate(promptTpl);
        return template.render(params);
    }
}
