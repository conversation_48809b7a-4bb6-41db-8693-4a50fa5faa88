package com.lazhu.business.mediaassets.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 素材更新请求DTO
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
public class MediaAssetUpdateRequest implements Serializable {

    private static final long serialVersionUID = 1L;


    private String source;

    private Long id;
    
    private String type;
    
    private String title;

    private String mediaUrl;

    private List<Long> ids;

    private List<Long> categoryIds;

    private String actorId;
}