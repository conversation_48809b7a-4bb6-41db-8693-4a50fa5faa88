package com.lazhu.business.mediaassets.strategy.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lazhu.business.actor.entity.Actor;
import com.lazhu.business.actor.entity.ActorQuery;
import com.lazhu.business.actor.service.ActorService;
import com.lazhu.business.mediaassets.dto.MediaAssetsDTO;
import com.lazhu.business.mediaassets.dto.MediaAssetsQuery;
import com.lazhu.business.mediaassets.entity.UserMediaAssets;
import com.lazhu.business.mediaassets.service.UserMediaAssetsService;
import com.lazhu.business.mediaassets.strategy.MediaAssetSourceStrategy;
import com.lazhu.common.enums.MediaAssetSourceEnum;
import com.lazhu.common.enums.MediaAssetTypeEnum;
import com.lazhu.common.media.MediaUtil;
import com.lazhu.common.utils.OssUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户上传素材策略实现
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Component
public class UserAssetSourceStrategy implements MediaAssetSourceStrategy {

    @Autowired
    private ActorService actorService;

    @Autowired
    private UserMediaAssetsService userMediaAssetsService;

    @Autowired
    private OssUtil ossUtil;

    @Override
    public Page<MediaAssetsDTO> queryAssets(MediaAssetsQuery query) {
        List<Actor> actors = Collections.emptyList();
        if (StrUtil.isNotBlank(query.getActorName())) {
            ActorQuery actorQuery = new ActorQuery();
            actorQuery.setNickName(query.getActorName());
            actorQuery.setCreateBy(query.getUserId());
            actors = actorService.queryList(BeanUtil.beanToMap(actorQuery));
            if (CollUtil.isEmpty(actors)) {
                return new Page<>();
            }
            List<Long> actorIds = actors.stream().map(Actor::getId).toList();
            query.setActorIds(actorIds);
        }
        // 执行查询
        Page<UserMediaAssets> userAssetsPage = userMediaAssetsService.queryPage(BeanUtil.beanToMap(query));
        if (CollUtil.isEmpty(actors)) {
            //设置角色名称
            List<Long> actorIds = userAssetsPage.getRecords().stream()
                    .filter(e -> e.getActorId() != null)
                    .map(UserMediaAssets::getActorId)
                    .collect(Collectors.toList());
            actors = actorService.selectByIds(actorIds);
        }
        Map<Long, Actor> actorMap = actors.stream().collect(Collectors.toMap(Actor::getId, e -> e));
        // 转换为统一素材对象
        List<MediaAssetsDTO> unifiedAssets = userAssetsPage.getRecords().stream().map(userAsset ->
                new MediaAssetsDTO(
                        userAsset.getId(),
                        userAsset.getAssetsType(),
                        MediaAssetSourceEnum.USER.getSource(),
                        userAsset.getTitle(),
                        userAsset.getMediaUrl(),
                        userAsset.getDurationMs(),
                        userAsset.getCoverImg(),
                        userAsset.getCreateTime(),
                        userAsset.getActorId(),
                        actorMap.get(userAsset.getActorId()) != null ? actorMap.get(userAsset.getActorId()).getNickName() : null
                )
        ).collect(Collectors.toList());

        // 创建返回的分页对象
        Page<MediaAssetsDTO> resultPage = new Page<>(userAssetsPage.getCurrent(), userAssetsPage.getSize());
        resultPage.setRecords(unifiedAssets);
        resultPage.setTotal(userAssetsPage.getTotal());

        return resultPage;
    }

    @Override
    public Long countAssets(MediaAssetsQuery query) {
        return userMediaAssetsService.count(BeanUtil.beanToMap(query));
    }

    @Override
    public MediaAssetsDTO uploadAsset(MediaAssetsDTO param) {
        UserMediaAssets userAsset = new UserMediaAssets();
        userAsset.setAssetsType(param.getAssetsType());
        userAsset.setTitle(param.getTitle());
        userAsset.setMediaUrl(param.getUrl());
        userAsset.setDurationMs(0L);
        userAsset.setCoverImg("");
        userAsset.setActorId(param.getActorId());
        userAsset.setCreateBy(param.getCreateBy());
        userAsset.setId(IdWorker.getId());
        //如果音频或者视频则获取时长和封面
        if (MediaAssetTypeEnum.AUDIO.getType().equals(param.getAssetsType())) {
            userAsset.setDurationMs(MediaUtil.getDuration(userAsset.getMediaUrl()));
        } else if (MediaAssetTypeEnum.VIDEO.getType().equals(param.getAssetsType())) {
            userAsset.setDurationMs(MediaUtil.getDuration(userAsset.getMediaUrl()));
            File videoCover = MediaUtil.getVideoCover(userAsset.getMediaUrl());
            String url = ossUtil.upload(videoCover);
            //删除临时文件
            FileUtil.del(videoCover);
            param.setCoverImg(url);
        }
        userMediaAssetsService.save(userAsset);

        return new MediaAssetsDTO(
                userAsset.getId(),
                userAsset.getAssetsType(),
                MediaAssetSourceEnum.USER.getSource(),
                userAsset.getTitle(),
                userAsset.getMediaUrl(),
                userAsset.getDurationMs(),
                userAsset.getCoverImg(),
                userAsset.getCreateTime()
        );
    }

    @Override
    public Boolean updateAsset(Long id, String title, String assetType) {
        UserMediaAssets userAsset = new UserMediaAssets();
        userAsset.setId(id);
        userAsset.setTitle(title);
        userAsset.setAssetsType(assetType);
        userMediaAssetsService.update(userAsset);
        return true;
    }

    @Override
    public Boolean batchDeleteAssets(List<Long> ids, String assetType) {
        return userMediaAssetsService.batchDelById(ids) > 0;
    }
}