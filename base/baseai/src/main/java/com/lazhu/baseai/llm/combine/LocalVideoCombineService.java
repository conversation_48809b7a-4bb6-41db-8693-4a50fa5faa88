package com.lazhu.baseai.llm.combine;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpUtil;
import com.lazhu.common.media.MediaUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.List;

@Slf4j
@Service
public class LocalVideoCombineService implements VideoCombineService {
    @Override
    public VideoCombineResult combine(VideoCombineReq req) {
        log.info(">>> 本地视频合成开始");
        String srtPath = req.getSrtPath();
        String outputPath = req.getOutputPath();
        List<String> videoUrls = req.getVideoUrl();
        File file = FileUtil.file(req.getOutputPath());
        File tmpFile = FileUtil.touch(file.getParent() + "/temp_combine." + FileUtil.extName(file.getName()));
        if (videoUrls.size() > 1) {
            //本地视频合成
            long start = System.currentTimeMillis();
            MediaUtil.merge(videoUrls, tmpFile.getAbsolutePath());
            log.info("===> 本地视频合成完成，视频地址：{},耗时：{} s", tmpFile.getAbsolutePath(), (System.currentTimeMillis() - start) / 1000);
        } else {
            //下载到本地
            HttpUtil.downloadFile(videoUrls.getFirst(), tmpFile);
            log.info("===> 视频下载完成，视频地址：{}", tmpFile.getAbsolutePath());
        }

        //字幕添加
        long start = System.currentTimeMillis();
        MediaUtil.addSubtitle(tmpFile.getAbsolutePath(), srtPath, outputPath);
        log.info("===> 字幕添加完成，视频地址：{},耗时：{} s", outputPath, (System.currentTimeMillis() - start / 1000));
        //删除临时文件
        FileUtil.del(tmpFile);
        VideoCombineResult result = new VideoCombineResult();
        result.setDone(true);
        result.setVideoPath(outputPath);
        return result;
    }
}
