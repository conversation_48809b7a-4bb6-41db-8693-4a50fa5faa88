//package com.lazhu.common.utils;
//
//import cn.hutool.core.io.FileUtil;
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.http.HttpUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.core.io.ClassPathResource;
//import org.springframework.util.StreamUtils;
//
//import java.io.*;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.function.Consumer;
//import java.util.function.Function;
//
//
//@Slf4j
//public class MediaUtil1 {
//
//    /**
//     * ffmpeg.exe 路径
//     */
//    private static String FFMPEG_PATH;
//
//    static {
//        try {
//            File ffmpegFile = getFFmpegBinary();
//            FFMPEG_PATH = ffmpegFile.getAbsolutePath();
//        } catch (Exception e) {
//            log.error("获取ffmpeg路径失败", e);
//            throw new RuntimeException(e);
//        }
//        log.info("获取ffmpeg路径 >> {}", FFMPEG_PATH);
//
//        // 注册JVM关闭钩子，确保程序退出时清理资源
//        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
//            log.info("JVM关闭，开始清理MediaUtil资源...");
//            cleanupAllTempFiles();
//            log.info("MediaUtil资源清理完成");
//        }, "MediaUtil-Cleanup-Thread"));
//    }
//
//    /**
//     * FFmpeg执行结果
//     */
//    public static class FFmpegResult {
//        private final int exitCode;
//        private final String output;
//        private final boolean success;
//        private final File outputFile;
//
//        public FFmpegResult(int exitCode, String output, File outputFile) {
//            this.exitCode = exitCode;
//            this.output = output;
//            this.success = exitCode == 0;
//            this.outputFile = outputFile;
//        }
//
//        public int getExitCode() { return exitCode; }
//        public String getOutput() { return output; }
//        public boolean isSuccess() { return success; }
//        public File getOutputFile() { return outputFile; }
//    }
//
//    /**
//     * FFmpeg命令构建器
//     */
//    public static class FFmpegCommandBuilder {
//        private final List<String> args = new ArrayList<>();
//
//        public FFmpegCommandBuilder() {
//            args.add(FFMPEG_PATH);
//        }
//
//        public FFmpegCommandBuilder input(String inputPath) {
//            args.add("-i");
//            args.add(inputPath);
//            return this;
//        }
//
//        public FFmpegCommandBuilder output(String outputPath) {
//            args.add(outputPath);
//            return this;
//        }
//
//        public FFmpegCommandBuilder overwrite() {
//            args.add("-y");
//            return this;
//        }
//
//        public FFmpegCommandBuilder videoCodec(String codec) {
//            args.add("-c:v");
//            args.add(codec);
//            return this;
//        }
//
//        public FFmpegCommandBuilder audioCodec(String codec) {
//            args.add("-c:a");
//            args.add(codec);
//            return this;
//        }
//
//        public FFmpegCommandBuilder quality(int crf) {
//            args.add("-crf");
//            args.add(String.valueOf(crf));
//            return this;
//        }
//
//        public FFmpegCommandBuilder videoFilter(String filter) {
//            args.add("-vf");
//            args.add(filter);
//            return this;
//        }
//
//        public FFmpegCommandBuilder seekStart(String time) {
//            args.add("-ss");
//            args.add(time);
//            return this;
//        }
//
//        public FFmpegCommandBuilder duration(String duration) {
//            args.add("-t");
//            args.add(duration);
//            return this;
//        }
//
//        public FFmpegCommandBuilder frames(int count) {
//            args.add("-vframes");
//            args.add(String.valueOf(count));
//            return this;
//        }
//
//        public FFmpegCommandBuilder addArg(String arg) {
//            args.add(arg);
//            return this;
//        }
//
//        public FFmpegCommandBuilder addArgs(String... arguments) {
//            for (String arg : arguments) {
//                args.add(arg);
//            }
//            return this;
//        }
//
//        public String[] build() {
//            return args.toArray(new String[0]);
//        }
//
//        public List<String> getArgs() {
//            return new ArrayList<>(args);
//        }
//    }
//
//    /**
//     * 执行FFmpeg命令的核心方法
//     *
//     * @param args FFmpeg命令参数
//     * @param outputFile 输出文件（可选，用于验证）
//     * @param progressCallback 进度回调（可选）
//     * @return FFmpeg执行结果
//     */
//    private static FFmpegResult executeFFmpegCommand(String[] args, File outputFile, Consumer<String> progressCallback) {
//        log.info("执行FFmpeg命令: {}", String.join(" ", args));
//
//        Process process = null;
//        BufferedReader reader = null;
//
//        try {
//            // 确保输出目录存在
//            if (outputFile != null) {
//                File outputDir = outputFile.getParentFile();
//                if (outputDir != null && !outputDir.exists()) {
//                    outputDir.mkdirs();
//                }
//            }
//
//            // 使用ProcessBuilder执行命令
//            process = new ProcessBuilder(args)
//                    .redirectErrorStream(true)
//                    .start();
//
//            // 读取输出信息
//            StringBuilder output = new StringBuilder();
//            reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
//
//            String line;
//            while ((line = reader.readLine()) != null) {
//                output.append(line).append("\n");
//
//                // 进度回调
//                if (progressCallback != null) {
//                    progressCallback.accept(line);
//                }
//
//                // 默认进度日志
//                if (line.contains("time=")) {
//                    log.debug("FFmpeg进度: {}", line);
//                }
//            }
//
//            // 等待命令执行完成
//            int exitCode = process.waitFor();
//
//            // 创建结果对象
//            FFmpegResult result = new FFmpegResult(exitCode, output.toString(), outputFile);
//
//            // 验证输出文件
//            if (outputFile != null && exitCode == 0) {
//                if (!outputFile.exists() || outputFile.length() == 0) {
//                    log.error("输出文件未生成或为空: {}", outputFile.getAbsolutePath());
//                    return new FFmpegResult(-1, output.toString() + "\n输出文件未生成或为空", outputFile);
//                }
//                log.info("FFmpeg执行成功，输出文件: {}, 大小: {}KB",
//                        outputFile.getAbsolutePath(), outputFile.length() / 1024);
//            }
//
//            return result;
//
//        } catch (Exception e) {
//            log.error("FFmpeg命令执行异常", e);
//            return new FFmpegResult(-1, "执行异常: " + e.getMessage(), outputFile);
//        } finally {
//            // 资源清理
//            cleanupResources(process, reader);
//        }
//    }
//
//    /**
//     * 执行FFmpeg命令（简化版本）
//     */
//    private static FFmpegResult executeFFmpegCommand(String[] args, File outputFile) {
//        return executeFFmpegCommand(args, outputFile, null);
//    }
//
//    /**
//     * 执行FFmpeg命令（无输出文件验证）
//     */
//    private static FFmpegResult executeFFmpegCommand(String[] args) {
//        return executeFFmpegCommand(args, null, null);
//    }
//
//    /**
//     * 清理进程和IO资源
//     *
//     * @param process 进程对象
//     * @param reader 读取器对象
//     */
//    private static void cleanupResources(Process process, BufferedReader reader) {
//        // 关闭读取器
//        if (reader != null) {
//            try {
//                reader.close();
//            } catch (Exception e) {
//                log.warn("关闭BufferedReader失败", e);
//            }
//        }
//
//        // 清理进程资源
//        if (process != null) {
//            try {
//                // 关闭进程的输入输出流
//                if (process.getInputStream() != null) {
//                    process.getInputStream().close();
//                }
//                if (process.getOutputStream() != null) {
//                    process.getOutputStream().close();
//                }
//                if (process.getErrorStream() != null) {
//                    process.getErrorStream().close();
//                }
//
//                // 如果进程还在运行，尝试正常终止
//                if (process.isAlive()) {
//                    log.warn("进程仍在运行，尝试终止...");
//                    process.destroy();
//
//                    // 等待一段时间后强制终止
//                    if (!process.waitFor(5, java.util.concurrent.TimeUnit.SECONDS)) {
//                        log.warn("进程未能正常终止，强制终止...");
//                        process.destroyForcibly();
//                    }
//                }
//            } catch (Exception e) {
//                log.warn("清理进程资源失败", e);
//            }
//        }
//    }
//
//    /**
//     * 根据视频/音频时长（秒）
//     */
//    public static Long getDuration(String url) {
//        log.info("获取视频/音频时长 >> url:{}", url);
//
//        String[] args = new FFmpegCommandBuilder()
//                .input(url)
//                .build();
//
//        FFmpegResult result = executeFFmpegCommand(args);
//
//        if (result.isSuccess()) {
//            return parseDurationFromOutput(result.getOutput());
//        } else {
//            log.error("获取视频/音频时长失败 >> url:{}, 错误信息:{}", url, result.getOutput());
//            return 0L;
//        }
//    }
//
//    /**
//     * 从FFmpeg输出中解析时长
//     */
//    private static Long parseDurationFromOutput(String output) {
//        try {
//            String[] lines = output.split("\n");
//            for (String line : lines) {
//                if (line.contains("Duration")) {
//                    String[] parts = line.split(",");
//                    String durationPart = parts[0].replace("Duration:", "").trim();
//                    String[] timeParts = durationPart.split(":");
//                    long hours = Long.parseLong(timeParts[0]);
//                    long minutes = Long.parseLong(timeParts[1]);
//                    double secondsWithMillis = Double.parseDouble(timeParts[2]);
//                    return (hours * 3600) + (minutes * 60) + (long) secondsWithMillis;
//                }
//            }
//        } catch (Exception e) {
//            log.error("解析时长失败", e);
//        }
//        return 0L;
//    }
//
//
//    /**
//     * 获取视频第一帧图片
//     */
//    public static File getVideoCover(String url) {
//        log.info("获取视频第一帧图片 >> url:{}", url);
//
//        try {
//            // 创建输出文件
//            File outputFile = createTempFile("video_cover_" + FileUtil.getPrefix(url), ".jpg");
//
//            // 构建FFmpeg命令
//            String[] args = new FFmpegCommandBuilder()
//                    .input(url)
//                    .seekStart("00:00:00")
//                    .frames(1)
//                    .addArgs("-q:v", "2")
//                    .overwrite()
//                    .output(outputFile.getAbsolutePath())
//                    .build();
//
//            // 执行命令
//            FFmpegResult result = executeFFmpegCommand(args, outputFile);
//
//            if (result.isSuccess()) {
//                return outputFile;
//            } else {
//                log.error("获取视频第一帧图片失败 >> url:{}, 错误信息:{}", url, result.getOutput());
//                return null;
//            }
//        } catch (Exception e) {
//            log.error("获取视频第一帧图片失败 >> url:{}", url, e);
//            return null;
//        }
//    }
//
//
//    /**
//     * 媒体截取
//     *
//     * @param url   视频地址
//     * @param start 开始时间 （秒）
//     * @param end   截取结束时间 （秒）
//     * @return 截取后的地址
//     */
//    public static String cut(String url, long start, long end) {
//        log.info("截取媒体文件 >> url:{}, start:{}, end:{}", url, start, end);
//
//        try {
//            // 创建输出文件
//            String fileName = FileUtil.getName(url);
//            String fileNameWithoutExt = FileUtil.getPrefix(fileName);
//            String extName = FileUtil.getSuffix(fileName);
//            File outputFile = createTempFile(fileNameWithoutExt + "_cut_" + start + "_" + end, "." + extName);
//
//            // 构建FFmpeg命令
//            String[] args = new FFmpegCommandBuilder()
//                    .seekStart(String.valueOf(start))
//                    .input(url)
//                    .duration(String.valueOf(end))
//                    .addArgs("-c", "copy")
//                    .overwrite()
//                    .output(outputFile.getAbsolutePath())
//                    .build();
//
//            // 执行命令
//            FFmpegResult result = executeFFmpegCommand(args, outputFile);
//
//            if (result.isSuccess()) {
//                log.info("截取媒体文件成功 >> url:{}, 输出文件:{}", url, outputFile.getAbsolutePath());
//                return outputFile.getAbsolutePath();
//            } else {
//                log.error("截取媒体文件失败 >> url:{}, 错误信息:{}", url, result.getOutput());
//                return null;
//            }
//        } catch (Exception e) {
//            log.error("截取媒体文件异常 >> url:{}", url, e);
//            throw new RuntimeException(e);
//        }
//    }
//
//    /**
//     * 视频合成
//     *
//     * @param urls    输入视频文件路径列表（支持本地文件路径和网络URL）
//     * @param outPath 输出文件路径
//     * @param urls    输入视频文件路径列表
//     * @param outPath 输出文件名
//     */
//    public static void merge(List<String> urls, String outPath) {
//        log.info("视频合成 >> 输入文件:{}", StrUtil.join(", ", urls));
//
//        if (urls == null || urls.isEmpty()) {
//            throw new IllegalArgumentException("输入视频列表不能为空");
//        }
//
//        if (urls.size() == 1) {
//            // 只有一个视频，直接复制或下载
//            String sourceUrl = urls.get(0);
//            try {
//                if (sourceUrl.startsWith("http://") || sourceUrl.startsWith("https://")) {
//                    // 网络URL，下载文件
//                    HttpUtil.downloadFile(sourceUrl, outPath);
//                } else {
//                    // 本地文件，复制文件
//                    FileUtil.copy(sourceUrl, outPath, true);
//                }
//                log.info("只有一个视频，直接复制到输出路径 >> 输出文件:{}", outPath);
//                return;
//            } catch (Exception e) {
//                log.error("复制单个视频文件失败 >> 错误信息:{}", e.getMessage());
//                throw new RuntimeException("复制单个视频文件失败", e);
//            }
//        }
//
//        // 多个视频需要合并，先下载网络视频到本地
//        List<String> localVideoPaths = new ArrayList<>();
//        List<File> tempFiles = new ArrayList<>();
//
//        try {
//            for (String url : urls) {
//                if (url.startsWith("http://") || url.startsWith("https://")) {
//                    // 下载网络视频到临时文件（不加入管理列表，手动管理）
//                    File tempFile = createTempFileUnmanaged("video_merge", ".mp4");
//                    HttpUtil.downloadFile(url, tempFile);
//                    localVideoPaths.add(tempFile.getAbsolutePath());
//                    tempFiles.add(tempFile);
//                    log.info("下载网络视频到临时文件 >> URL:{}, 临时文件:{}", url, tempFile.getAbsolutePath());
//                } else {
//                    // 本地文件路径
//                    localVideoPaths.add(url);
//                }
//            }
//
//            // 创建列表文件（不加入管理列表，手动管理）
//            File listFile = createTempFileUnmanaged("listfile", ".txt");
//            tempFiles.add(listFile);
//
//            try (BufferedWriter writer = new BufferedWriter(new FileWriter(listFile))) {
//                for (String path : localVideoPaths) {
//                    writer.write("file '" + path.replace("'", "'\\''") + "'");
//                    writer.newLine();
//                }
//            } catch (IOException e) {
//                log.error("创建列表文件失败 >> 错误信息:{}", e.getMessage());
//                throw new RuntimeException("创建列表文件失败", e);
//            }
//
//            // 尝试使用快速合并（仅复制流）
//            if (tryFastMerge(listFile.getAbsolutePath(), outPath)) {
//                log.info("视频合成成功 >> 输出文件:{}", outPath);
//            }
//
//        } finally {
//            // 清理临时文件
//            for (File tempFile : tempFiles) {
//                cleanupTempFile(tempFile);
//            }
//        }
//    }
//
//    /**
//     * 尝试快速合并（仅复制流，要求编码格式一致）
//     */
//    private static boolean tryFastMerge(String listFilePath, String outPath) {
//        try {
//            File outputFile = new File(outPath);
//
//            // 构建FFmpeg命令
//            String[] args = new FFmpegCommandBuilder()
//                    .addArgs("-f", "concat")
//                    .addArgs("-safe", "0")
//                    .input(listFilePath)
//                    .addArgs("-c", "copy")
//                    .overwrite()
//                    .output(outPath)
//                    .build();
//
//            // 执行命令
//            FFmpegResult result = executeFFmpegCommand(args, outputFile);
//
//            if (result.isSuccess()) {
//                log.info("快速视频合并成功");
//                return true;
//            } else {
//                log.warn("快速视频合并失败 >> 错误信息:{}", result.getOutput());
//                return false;
//            }
//        } catch (Exception e) {
//            log.warn("快速视频合并异常 >> 错误信息:{}", e.getMessage());
//            return false;
//        }
//    }
//
//    /**
//     * 添加字幕到视频
//     *
//     * @param videoUrl     视频路径
//     * @param subtitlePath 字幕文件路径
//     * @param outPath      输出文件路径
//     */
//    public static void addSubtitle(String videoUrl, String subtitlePath, String outPath) {
//        log.info("添加字幕到视频 >> 视频路径:{}, 字幕文件:{}", videoUrl, subtitlePath);
//
//        try {
//            // 处理字幕路径
//            String processedSubtitlePath = processPathForFFmpeg(subtitlePath);
//            File outputFile = new File(outPath);
//
//            // 构建FFmpeg命令
//            String[] args = new FFmpegCommandBuilder()
//                    .overwrite()
//                    .input(videoUrl)
//                    .videoFilter("subtitles='" + processedSubtitlePath + "'")
//                    .output(outPath)
//                    .build();
//
//            // 执行命令
//            FFmpegResult result = executeFFmpegCommand(args, outputFile);
//
//            if (result.isSuccess()) {
//                log.info("添加字幕成功 >> 视频路径:{}, 输出文件:{}", videoUrl, outPath);
//            } else {
//                log.error("添加字幕失败 >> 视频路径:{}, 字幕文件:{}, 错误信息:{}", videoUrl, subtitlePath, result.getOutput());
//                throw new RuntimeException("添加字幕失败");
//            }
//        } catch (Exception e) {
//            log.error("添加字幕异常 >> 视频路径:{}, 字幕文件:{}", videoUrl, subtitlePath, e);
//            throw new RuntimeException("添加字幕异常", e);
//        }
//    }
//
//    /**
//     * 合成ASS字幕到视频（硬字幕烧录）
//     *
//     * @param videoUrl     视频路径（支持本地文件和网络URL）
//     * @param assPath      ASS字幕文件路径
//     * @param outPath      输出文件路径
//     * @param fontsDir     字体目录路径（可选，为null时使用系统默认字体目录）
//     * @param quality      视频质量（CRF值，18-28，数值越小质量越高，可选，默认23）
//     */
//    public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath, String fontsDir, Integer quality) {
//        log.info("合成ASS字幕到视频 >> 视频路径:{}, ASS字幕文件:{}, 输出路径:{}", videoUrl, assPath, outPath);
//
//        // 参数验证
//        validateParameters(videoUrl, assPath, outPath);
//        validateFileExists(assPath, "ASS字幕文件不存在");
//
//        try {
//            File outputFile = new File(outPath);
//
//            // 构建视频滤镜参数
//            String filter = buildAssFilter(assPath, fontsDir);
//
//            // 设置默认质量
//            if (quality == null || quality < 0 || quality > 51) {
//                quality = 23;
//            }
//
//            // 构建FFmpeg命令
//            String[] args = new FFmpegCommandBuilder()
//                    .input(videoUrl)
//                    .videoFilter(filter)
//                    .videoCodec("libx264")
//                    .quality(quality)
//                    .audioCodec("copy")
//                    .overwrite()
//                    .output(outPath)
//                    .build();
//
//            // 执行命令
//            FFmpegResult result = executeFFmpegCommand(args, outputFile);
//
//            if (result.isSuccess()) {
//                log.info("合成ASS字幕成功 >> 视频路径:{}, 输出文件:{}, 文件大小:{}KB",
//                        videoUrl, outPath, outputFile.length() / 1024);
//            } else {
//                log.error("合成ASS字幕失败 >> 视频路径:{}, ASS字幕文件:{}, 错误信息:{}", videoUrl, assPath, result.getOutput());
//                throw new RuntimeException("合成ASS字幕失败，退出码: " + result.getExitCode());
//            }
//        } catch (Exception e) {
//            log.error("合成ASS字幕异常 >> 视频路径:{}, ASS字幕文件:{}", videoUrl, assPath, e);
//            throw new RuntimeException("合成ASS字幕异常", e);
//        }
//    }
//
//    /**
//     * 合成ASS字幕到视频（使用默认参数）
//     *
//     * @param videoUrl 视频路径
//     * @param assPath  ASS字幕文件路径
//     * @param outPath  输出文件路径
//     */
//    public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath) {
//        mergeAssSubtitle(videoUrl, assPath, outPath, null, null);
//    }
//
//    /**
//     * 添加ASS字幕作为软字幕流到视频
//     *
//     * @param videoUrl 视频路径
//     * @param assPath  ASS字幕文件路径
//     * @param outPath  输出文件路径（建议使用.mkv格式以获得最佳兼容性）
//     * @param language 字幕语言标识（可选，如"chi"、"eng"等）
//     * @param title    字幕标题（可选，如"中文"、"English"等）
//     */
//    public static void addAssSoftSubtitle(String videoUrl, String assPath, String outPath, String language, String title) {
//        log.info("添加ASS软字幕到视频 >> 视频路径:{}, ASS字幕文件:{}, 输出路径:{}", videoUrl, assPath, outPath);
//
//        // 参数验证
//        validateParameters(videoUrl, assPath, outPath);
//        validateFileExists(assPath, "ASS字幕文件不存在");
//
//        try {
//            File outputFile = new File(outPath);
//
//            // 构建FFmpeg命令
//            FFmpegCommandBuilder builder = new FFmpegCommandBuilder()
//                    .input(videoUrl)
//                    .input(assPath)
//                    .addArgs("-c", "copy")
//                    .addArgs("-c:s", "ass");
//
//            // 添加字幕元数据
//            if (StrUtil.isNotBlank(language)) {
//                builder.addArgs("-metadata:s:s:0", "language=" + language);
//            }
//            if (StrUtil.isNotBlank(title)) {
//                builder.addArgs("-metadata:s:s:0", "title=" + title);
//            }
//
//            String[] args = builder.overwrite().output(outPath).build();
//
//            // 执行命令
//            FFmpegResult result = executeFFmpegCommand(args, outputFile);
//
//            if (result.isSuccess()) {
//                log.info("添加ASS软字幕成功 >> 视频路径:{}, 输出文件:{}, 文件大小:{}KB",
//                        videoUrl, outPath, outputFile.length() / 1024);
//            } else {
//                log.error("添加ASS软字幕失败 >> 视频路径:{}, ASS字幕文件:{}, 错误信息:{}", videoUrl, assPath, result.getOutput());
//                throw new RuntimeException("添加ASS软字幕失败，退出码: " + result.getExitCode());
//            }
//        } catch (Exception e) {
//            log.error("添加ASS软字幕异常 >> 视频路径:{}, ASS字幕文件:{}", videoUrl, assPath, e);
//            throw new RuntimeException("添加ASS软字幕异常", e);
//        }
//    }
//
//    /**
//     * 添加ASS字幕作为软字幕流到视频（使用默认参数）
//     *
//     * @param videoUrl 视频路径
//     * @param assPath  ASS字幕文件路径
//     * @param outPath  输出文件路径
//     */
//    public static void addAssSoftSubtitle(String videoUrl, String assPath, String outPath) {
//        addAssSoftSubtitle(videoUrl, assPath, outPath, null, null);
//    }
//
//    // ==================== 辅助方法 ====================
//
//    /**
//     * 临时文件管理器
//     */
//    private static final List<File> TEMP_FILES = new ArrayList<>();
//
//    /**
//     * 创建临时文件并加入管理列表
//     */
//    private static File createTempFile(String prefix, String suffix) {
//        try {
//            String tempDir = System.getProperty("java.io.tmpdir");
//            File file = new File(tempDir, prefix + "_" + System.currentTimeMillis() + suffix);
//
//            // 清理已存在的文件
//            if (file.exists()) {
//                FileUtil.del(file);
//            }
//
//            // 添加到临时文件管理列表
//            synchronized (TEMP_FILES) {
//                TEMP_FILES.add(file);
//            }
//
//            return file;
//        } catch (Exception e) {
//            throw new RuntimeException("创建临时文件失败", e);
//        }
//    }
//
//    /**
//     * 创建临时文件（不加入管理列表）
//     */
//    private static File createTempFileUnmanaged(String prefix, String suffix) {
//        try {
//            String tempDir = System.getProperty("java.io.tmpdir");
//            File file = new File(tempDir, prefix + "_" + System.currentTimeMillis() + suffix);
//
//            // 清理已存在的文件
//            if (file.exists()) {
//                FileUtil.del(file);
//            }
//
//            return file;
//        } catch (Exception e) {
//            throw new RuntimeException("创建临时文件失败", e);
//        }
//    }
//
//    /**
//     * 清理指定的临时文件
//     */
//    public static void cleanupTempFile(File file) {
//        if (file != null && file.exists()) {
//            try {
//                FileUtil.del(file);
//                synchronized (TEMP_FILES) {
//                    TEMP_FILES.remove(file);
//                }
//                log.debug("清理临时文件: {}", file.getAbsolutePath());
//            } catch (Exception e) {
//                log.warn("清理临时文件失败: {}", file.getAbsolutePath(), e);
//            }
//        }
//    }
//
//    /**
//     * 清理所有临时文件
//     */
//    public static void cleanupAllTempFiles() {
//        synchronized (TEMP_FILES) {
//            for (File file : new ArrayList<>(TEMP_FILES)) {
//                cleanupTempFile(file);
//            }
//            TEMP_FILES.clear();
//        }
//        log.info("已清理所有临时文件");
//    }
//
//    /**
//     * 获取当前临时文件数量
//     */
//    public static int getTempFileCount() {
//        synchronized (TEMP_FILES) {
//            return TEMP_FILES.size();
//        }
//    }
//
//    /**
//     * 参数验证
//     */
//    private static void validateParameters(String... params) {
//        for (String param : params) {
//            if (StrUtil.isBlank(param)) {
//                throw new IllegalArgumentException("参数不能为空");
//            }
//        }
//    }
//
//    /**
//     * 文件存在性验证
//     */
//    private static void validateFileExists(String filePath, String errorMessage) {
//        File file = new File(filePath);
//        if (!file.exists()) {
//            throw new IllegalArgumentException(errorMessage + ": " + filePath);
//        }
//    }
//
//    /**
//     * 构建ASS滤镜参数
//     */
//    private static String buildAssFilter(String assPath, String fontsDir) {
//        String processedAssPath = processPathForFFmpeg(assPath);
//
//        StringBuilder filterBuilder = new StringBuilder();
//        filterBuilder.append("ass=").append(processedAssPath);
//
//        // 添加字体目录参数
//        if (StrUtil.isNotBlank(fontsDir)) {
//            String processedFontsDir = processPathForFFmpeg(fontsDir);
//            filterBuilder.append(":fontsdir=").append(processedFontsDir);
//        } else {
//            // 使用系统默认字体目录
//            String defaultFontsDir = getDefaultFontsDir();
//            if (StrUtil.isNotBlank(defaultFontsDir)) {
//                String processedFontsDir = processPathForFFmpeg(defaultFontsDir);
//                filterBuilder.append(":fontsdir=").append(processedFontsDir);
//            }
//        }
//
//        return filterBuilder.toString();
//    }
//
//    /**
//     * 处理文件路径以适配FFmpeg的要求
//     * 主要处理Windows路径的转义和特殊字符
//     *
//     * @param path 原始路径
//     * @return 处理后的路径
//     */
//    private static String processPathForFFmpeg(String path) {
//        if (StrUtil.isBlank(path)) {
//            return path;
//        }
//
//        // 统一使用正斜杠
//        String processedPath = path.replace("\\", "/");
//
//        // Windows路径处理
//        if (isWindows() && processedPath.matches("^[A-Za-z]:.*")) {
//            // 将 C:/path 转换为 C\:/path 格式
//            processedPath = processedPath.charAt(0) + "\\:" + processedPath.substring(2);
//        }
//
//        // 处理路径中的特殊字符，用单引号包围
//        if (processedPath.contains(" ") || processedPath.contains("'") || processedPath.contains("(") || processedPath.contains(")")) {
//            // 转义单引号
//            processedPath = processedPath.replace("'", "'\\''");
//            processedPath = "'" + processedPath + "'";
//        }
//
//        return processedPath;
//    }
//
//    /**
//     * 获取系统默认字体目录
//     *
//     * @return 字体目录路径
//     */
//    private static String getDefaultFontsDir() {
//        String osName = System.getProperty("os.name").toLowerCase();
//
//        if (osName.contains("win")) {
//            // Windows系统
//            return "C:/Windows/Fonts";
//        } else if (osName.contains("mac")) {
//            // macOS系统
//            return "/System/Library/Fonts";
//        } else {
//            // Linux系统
//            return "/usr/share/fonts";
//        }
//    }
//
//
//    private static final String ExecutablePath = "/data";
//
//    /**
//     * 获取ffmpeg
//     */
//    private static File getFFmpegBinary() throws Exception {
//        // 根据操作系统确定文件后缀
//        String suffix = isWindows() ? ".exe" : "";
//
//        // 创建临时文件
//        File ffmpegFile = FileUtil.file(FileUtil.getTmpDirPath() + "/ffmpeg" + suffix);
//        if (ffmpegFile.exists()) {
//            return ffmpegFile;
//        }
//        ffmpegFile = FileUtil.touch(FileUtil.getTmpDirPath() + "/ffmpeg" + suffix);
//
//
//        // 从classpath资源加载ffmpeg
//        String fileName = isWindows() ? "ffmpeg.exe" : "ffmpeg";
//        ClassPathResource resource = new ClassPathResource(fileName);
//        try (InputStream in = resource.getInputStream();
//             FileOutputStream out = new FileOutputStream(ffmpegFile)) {
//            // 复制资源到临时文件
//            StreamUtils.copy(in, out);
//        }
//
//        // 非Windows系统设置可执行权限
//        if (!isWindows()) {
//            if (ffmpegFile.setExecutable(true)) {
//                log.info("设置ffmpeg可执行权限成功");
//            } else {
//                throw new RuntimeException("无法设置ffmpeg可执行权限");
//            }
//        }
//        return ffmpegFile;
//    }
//
//    private static boolean isWindows() {
//        return System.getProperty("os.name").toLowerCase().contains("win");
//    }
//
//    public static void main(String[] args) {
////        String videoUrl = "http://dashscope-result-sh.oss-cn-shanghai.aliyuncs.com/1d/ba/20250624/30eefb8a/1d1f2dbe-13f3-4521-908c-5cc53998dd4e_vidretalk.mp4?Expires=1750839816&OSSAccessKeyId=LTAI5tKPD3TMqf2Lna1fASuh&Signature=5Nb4dmnaC%2BYl4BKsky8Z%2BfHCLdQ%3D";
////        HttpUtil.downloadFile(videoUrl, "C:\\Users\\<USER>\\AppData\\Local\\Temp\\voice\\1935678324591476737\\tt.mp4");
//        String videoPath = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\content_combine\\1935678324591476737\\temp_combine.mp4";
//        Long duration = getDuration(videoPath);
//        System.out.println(duration);
//        System.out.println(getVideoCover(videoPath));
//        String subtitlePath = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\voice\\1935678324591476737\\audio.srt";
////        addSubtitle(videoPath, subtitlePath);
////        String path = URLUtil.getPath(videoUrl);
////        System.out.println(path);
//
//        // ASS字幕合成测试示例
////        String assPath = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\voice\\1935678324591476737\\subtitle.ass";
////        String outputPath = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\voice\\1935678324591476737\\video_with_ass.mp4";
////
////        // 硬字幕烧录（推荐用于最终输出）
////        mergeAssSubtitle(videoPath, assPath, outputPath);
////
////        // 软字幕嵌入（推荐用于保留字幕可控性）
////        String mkvOutputPath = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\voice\\1935678324591476737\\video_with_soft_ass.mkv";
////        addAssSoftSubtitle(videoPath, assPath, mkvOutputPath, "chi", "中文字幕");
//    }
//
//
//}
