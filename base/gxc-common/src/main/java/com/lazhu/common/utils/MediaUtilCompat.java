package com.lazhu.common.utils;

import com.lazhu.common.media.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.List;

/**
 * MediaUtil 兼容性包装类
 * 保持原有 MediaUtil 的 API 不变，内部委托给新的拆分后的服务类
 * 确保现有代码无需修改即可使用
 * 
 * <AUTHOR>
 * @date 2025-08-04
 * @deprecated 建议直接使用具体的服务类，如 VideoInfoService, VideoProcessService 等
 */
@Slf4j
@Deprecated
public class MediaUtilCompat {

    // ==================== 视频信息相关方法 ====================

    /**
     * 获取视频/音频时长（秒）
     * 
     * @deprecated 使用 {@link VideoInfoService#getDuration(String)} 替代
     */
    @Deprecated
    public static Long getDuration(String url) {
        return VideoInfoService.getDuration(url);
    }

    /**
     * 获取视频第一帧图片
     * 
     * @deprecated 使用 {@link VideoInfoService#getVideoCover(String)} 替代
     */
    @Deprecated
    public static File getVideoCover(String url) {
        return VideoInfoService.getVideoCover(url);
    }

    // ==================== 视频处理相关方法 ====================

    /**
     * 媒体截取
     *
     * @param url   视频地址
     * @param start 开始时间（秒）
     * @param end   截取结束时间（秒）
     * @return 截取后的地址
     * 
     * @deprecated 使用 {@link VideoProcessService#cut(String, long, long)} 替代
     */
    @Deprecated
    public static String cut(String url, long start, long end) {
        return VideoProcessService.cut(url, start, end);
    }

    /**
     * 视频合成
     *
     * @param urls    输入视频文件路径列表（支持本地文件路径和网络URL）
     * @param outPath 输出文件路径
     * 
     * @deprecated 使用 {@link VideoProcessService#merge(List, String)} 替代
     */
    @Deprecated
    public static void merge(List<String> urls, String outPath) {
        VideoProcessService.merge(urls, outPath);
    }

    // ==================== 字幕相关方法 ====================

    /**
     * 添加字幕到视频
     *
     * @param videoUrl     视频路径
     * @param subtitlePath 字幕文件路径
     * @param outPath      输出文件路径
     * 
     * @deprecated 使用 {@link SubtitleService#addSubtitle(String, String, String)} 替代
     */
    @Deprecated
    public static void addSubtitle(String videoUrl, String subtitlePath, String outPath) {
        SubtitleService.addSubtitle(videoUrl, subtitlePath, outPath);
    }

    /**
     * 合成ASS字幕到视频（硬字幕烧录）
     *
     * @param videoUrl 视频路径（支持本地文件和网络URL）
     * @param assPath  ASS字幕文件路径
     * @param outPath  输出文件路径
     * @param fontsDir 字体目录路径（可选，为null时使用系统默认字体目录）
     * @param quality  视频质量（CRF值，18-28，数值越小质量越高，可选，默认23）
     * 
     * @deprecated 使用 {@link SubtitleService#mergeAssSubtitle(String, String, String, String, Integer)} 替代
     */
    @Deprecated
    public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath, 
                                       String fontsDir, Integer quality) {
        SubtitleService.mergeAssSubtitle(videoUrl, assPath, outPath, fontsDir, quality);
    }

    /**
     * 合成ASS字幕到视频（使用默认参数）
     *
     * @param videoUrl 视频路径
     * @param assPath  ASS字幕文件路径
     * @param outPath  输出文件路径
     * 
     * @deprecated 使用 {@link SubtitleService#mergeAssSubtitle(String, String, String)} 替代
     */
    @Deprecated
    public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath) {
        SubtitleService.mergeAssSubtitle(videoUrl, assPath, outPath);
    }

    /**
     * 添加ASS字幕作为软字幕流到视频
     *
     * @param videoUrl 视频路径
     * @param assPath  ASS字幕文件路径
     * @param outPath  输出文件路径（建议使用.mkv格式以获得最佳兼容性）
     * @param language 字幕语言标识（可选，如"chi"、"eng"等）
     * @param title    字幕标题（可选，如"中文"、"English"等）
     * 
     * @deprecated 使用 {@link SubtitleService#addAssSoftSubtitle(String, String, String, String, String)} 替代
     */
    @Deprecated
    public static void addAssSoftSubtitle(String videoUrl, String assPath, String outPath, 
                                         String language, String title) {
        SubtitleService.addAssSoftSubtitle(videoUrl, assPath, outPath, language, title);
    }

    /**
     * 添加ASS字幕作为软字幕流到视频（使用默认参数）
     *
     * @param videoUrl 视频路径
     * @param assPath  ASS字幕文件路径
     * @param outPath  输出文件路径
     * 
     * @deprecated 使用 {@link SubtitleService#addAssSoftSubtitle(String, String, String)} 替代
     */
    @Deprecated
    public static void addAssSoftSubtitle(String videoUrl, String assPath, String outPath) {
        SubtitleService.addAssSoftSubtitle(videoUrl, assPath, outPath);
    }

    // ==================== 资源管理相关方法 ====================

    /**
     * 清理指定的临时文件
     * 
     * @deprecated 使用 {@link MediaResourceManager#cleanupTempFile(File)} 替代
     */
    @Deprecated
    public static void cleanupTempFile(File file) {
        MediaResourceManager.cleanupTempFile(file);
    }

    /**
     * 清理所有临时文件
     * 
     * @deprecated 使用 {@link MediaResourceManager#cleanupAllTempFiles()} 替代
     */
    @Deprecated
    public static void cleanupAllTempFiles() {
        MediaResourceManager.cleanupAllTempFiles();
    }

    /**
     * 获取当前临时文件数量
     * 
     * @deprecated 使用 {@link MediaResourceManager#getTempFileCount()} 替代
     */
    @Deprecated
    public static int getTempFileCount() {
        return MediaResourceManager.getTempFileCount();
    }

    // ==================== 新增的便捷方法 ====================

    /**
     * 获取视频详细信息
     */
    public static VideoInfoService.VideoInfo getVideoInfo(String url) {
        return VideoInfoService.getVideoInfo(url);
    }

    /**
     * 获取视频指定时间点的截图
     */
    public static File getVideoScreenshot(String url, long timeSeconds) {
        return VideoInfoService.getVideoScreenshot(url, timeSeconds);
    }

    /**
     * 视频格式转换
     */
    public static void convertVideo(String inputPath, String outputPath, String videoCodec, 
                                   String audioCodec, Integer quality) {
        VideoProcessService.convert(inputPath, outputPath, videoCodec, audioCodec, quality);
    }

    /**
     * 视频格式转换（使用默认参数）
     */
    public static void convertVideo(String inputPath, String outputPath) {
        VideoProcessService.convert(inputPath, outputPath);
    }

    /**
     * 调整视频分辨率
     */
    public static void resizeVideo(String inputPath, String outputPath, int width, int height) {
        VideoProcessService.resize(inputPath, outputPath, width, height);
    }

    /**
     * 获取资源使用统计
     */
    public static MediaResourceManager.TempFileStats getResourceStats() {
        return MediaResourceManager.getTempFileStats();
    }

    /**
     * 执行资源维护清理
     */
    public static void performMaintenanceCleanup() {
        MediaResourceManager.performMaintenanceCleanup();
    }

    // ==================== 迁移指南 ====================

    /**
     * 打印迁移指南
     */
    public static void printMigrationGuide() {
        log.info("=== MediaUtil 迁移指南 ===");
        log.info("原 MediaUtil 已拆分为以下服务类：");
        log.info("1. VideoInfoService - 视频信息获取（时长、封面、详细信息等）");
        log.info("2. VideoProcessService - 视频处理（截取、合并、转换、调整分辨率等）");
        log.info("3. SubtitleService - 字幕处理（添加字幕、ASS字幕合成等）");
        log.info("4. MediaResourceManager - 资源管理（临时文件管理、清理等）");
        log.info("5. FFmpegExecutor - FFmpeg执行核心");
        log.info("6. MediaPathUtils - 路径处理工具");
        log.info("");
        log.info("建议迁移方式：");
        log.info("- 将 MediaUtil.getDuration() 替换为 VideoInfoService.getDuration()");
        log.info("- 将 MediaUtil.cut() 替换为 VideoProcessService.cut()");
        log.info("- 将 MediaUtil.mergeAssSubtitle() 替换为 SubtitleService.mergeAssSubtitle()");
        log.info("- 将 MediaUtil.cleanupAllTempFiles() 替换为 MediaResourceManager.cleanupAllTempFiles()");
        log.info("");
        log.info("当前使用的是兼容性包装类，建议尽快迁移到新的服务类");
        log.info("=== 迁移指南结束 ===");
    }

    static {
        // 在类加载时提示迁移
        log.warn("正在使用 MediaUtilCompat 兼容性包装类，建议迁移到新的服务类");
        log.warn("调用 MediaUtilCompat.printMigrationGuide() 查看详细迁移指南");
    }
}
