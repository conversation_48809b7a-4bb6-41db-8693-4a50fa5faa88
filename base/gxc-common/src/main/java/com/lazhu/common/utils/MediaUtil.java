package com.lazhu.common.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;


@Slf4j
public class MediaUtil {

    /**
     * ffmpeg.exe 路径
     */
    private static String FFMPEG_PATH;

    static {
        try {
            File ffmpegFile = getFFmpegBinary();
            FFMPEG_PATH = ffmpegFile.getAbsolutePath();
        } catch (Exception e) {
            log.error("获取ffmpeg路径失败", e);
            throw new RuntimeException(e);
        }
        log.info("获取ffmpeg路径 >> {}", FFMPEG_PATH);
    }

    /**
     * 根据视频/音频时长（秒）
     */
    public static Long getDuration(String url) {
        log.info("获取视频/音频时长 >> url:{}", url);
        try {
            Process process = Runtime.getRuntime().exec(new String[]{FFMPEG_PATH, "-i", url});
            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getErrorStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains("Duration")) {
                    String[] parts = line.split(",");
                    String durationPart = parts[0].replace("Duration:", "").trim();
                    String[] timeParts = durationPart.split(":");
                    long hours = Long.parseLong(timeParts[0]);
                    long minutes = Long.parseLong(timeParts[1]);
                    double secondsWithMillis = Double.parseDouble(timeParts[2]);
                    return (hours * 3600) + (minutes * 60) + (long) secondsWithMillis;
                }
            }
        } catch (Exception e) {
            log.error("获取视频/音频时长失败 >> url:{}", url, e);
        }
        return 0L; // Default if duration could not be parsed
    }


    /**
     * 获取视频第一帧图片
     */
    public static File getVideoCover(String url) {
        log.info("获取视频第一帧图片 >> url:{}", url);

        try {
            // 使用系统临时目录+随机文件名
            String tempDir = System.getProperty("java.io.tmpdir");
            String fileName = "video_cover_" + FileUtil.getPrefix(url) + ".jpg";
            File file = new File(tempDir, fileName);

            // 清理已存在的文件
            if (file.exists()) {
                FileUtil.del(file);
            }

            // 简化FFmpeg命令（移除不必要的转义和引号）
            String[] args = new String[]{
                    FFMPEG_PATH,
                    "-i", url,
                    "-ss", "00:00:00",  // 定位到起始位置
                    "-vframes", "1",    // 只取一帧
                    "-q:v", "2",        // 输出质量
                    "-y",               // 覆盖输出文件
                    file.getAbsolutePath()
            };

            log.info("执行ffmpeg命令:{}", String.join(" ", args));

            // 使用ProcessBuilder替代Runtime.exec
            Process process = new ProcessBuilder(args)
                    .redirectErrorStream(true)
                    .start();

            // 等待命令完成并检查退出码
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new IOException("FFmpeg process exited with code " + exitCode);
            }

            // 验证文件是否生成
            if (!file.exists() || file.length() == 0) {
                throw new IOException("Generated cover file is empty or not created");
            }

            return file;
        } catch (Exception e) {
            log.error("获取视频第一帧图片失败 >> url:{}", url, e);
            return null;
        }
    }


    /**
     * 媒体截取
     *
     * @param url   视频地址
     * @param start 开始时间 （秒）
     * @param end   截取结束时间 （秒）
     * @return 截取后的地址
     */
    public static String cut(String url, long start, long end) {
        log.info("截取媒体文件 >> url:{}, start:{}, end:{}", url, start, end);

        // 获取文件名和扩展名
        String fileName = FileUtil.getName(url);
        String fileNameWithoutExt = FileUtil.getPrefix(fileName);
        String extName = FileUtil.getSuffix(fileName);

        // 创建临时文件
        File tempFile = FileUtil.createTempFile(fileNameWithoutExt + "_cut_" + start + "_" + end, "." + extName, null, true);
        if (tempFile.exists()) {
            tempFile.delete();
        }

        try {
            // 构建ffmpeg命令
            // -ss 参数指定开始时间，-t 参数指定持续时间，-c copy 参数指定复制编解码器（不重新编码，提高速度）
            String[] args = new String[]{
                    FFMPEG_PATH,
                    "-ss", start + "",
                    "-i", url,
                    "-t", end + "",
                    "-c", "copy",
                    tempFile.getAbsolutePath()
            };

            log.info("执行ffmpeg命令:{}", StrUtil.join(" ", args));

            // 执行命令
            Process process = Runtime.getRuntime().exec(args);

            // 获取错误输出
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            String line;
            StringBuilder errorOutput = new StringBuilder();
            while ((line = errorReader.readLine()) != null) {
                errorOutput.append(line).append("\n");
            }

            // 等待命令执行完成
            int exitCode = process.waitFor();

            // 检查命令是否成功执行
            if (exitCode != 0) {
                log.error("截取媒体文件失败 >> url:{}, 错误信息:{}", url, errorOutput.toString());
                return null;
            }

            // 返回临时文件的绝对路径
            log.info("截取媒体文件成功 >> url:{}, 输出文件:{}", url, tempFile.getAbsolutePath());
            return tempFile.getAbsolutePath();
        } catch (Exception e) {
            log.error("截取媒体文件异常 >> url:{}", url, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 视频合成
     *
     * @param urls    输入视频文件路径列表（支持本地文件路径和网络URL）
     * @param outPath 输出文件路径
     * @param urls    输入视频文件路径列表
     * @param outPath 输出文件名
     */
    public static void merge(List<String> urls, String outPath) {
        log.info("视频合成 >> 输入文件:{}", StrUtil.join(", ", urls));

        if (urls == null || urls.isEmpty()) {
            throw new IllegalArgumentException("输入视频列表不能为空");
        }

        if (urls.size() == 1) {
            // 只有一个视频，直接复制或下载
            String sourceUrl = urls.get(0);
            try {
                if (sourceUrl.startsWith("http://") || sourceUrl.startsWith("https://")) {
                    // 网络URL，下载文件
                    HttpUtil.downloadFile(sourceUrl, outPath);
                } else {
                    // 本地文件，复制文件
                    FileUtil.copy(sourceUrl, outPath, true);
                }
                log.info("只有一个视频，直接复制到输出路径 >> 输出文件:{}", outPath);
                return;
            } catch (Exception e) {
                log.error("复制单个视频文件失败 >> 错误信息:{}", e.getMessage());
                throw new RuntimeException("复制单个视频文件失败", e);
            }
        }

        // 多个视频需要合并，先下载网络视频到本地
        List<String> localVideoPaths = new ArrayList<>();
        List<File> tempFiles = new ArrayList<>();

        try {
            for (String url : urls) {
                if (url.startsWith("http://") || url.startsWith("https://")) {
                    // 下载网络视频到临时文件
                    File tempFile = FileUtil.createTempFile("video_merge_" + System.currentTimeMillis(), ".mp4", null, true);
                    HttpUtil.downloadFile(url, tempFile);
                    localVideoPaths.add(tempFile.getAbsolutePath());
                    tempFiles.add(tempFile);
                    log.info("下载网络视频到临时文件 >> URL:{}, 临时文件:{}", url, tempFile.getAbsolutePath());
                } else {
                    // 本地文件路径
                    localVideoPaths.add(url);
                }
            }

            // 创建列表文件
            File listFile = FileUtil.createTempFile("listfile_" + System.currentTimeMillis(), ".txt", null, false);
            tempFiles.add(listFile); // 添加到临时文件列表以便清理
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(listFile))) {
                for (String path : localVideoPaths) {
                    writer.write("file '" + path.replace("'", "'\\''") + "'");
                    writer.newLine();
                }
            } catch (IOException e) {
                log.error("创建列表文件失败 >> 错误信息:{}", e.getMessage());
                throw new RuntimeException("创建列表文件失败", e);
            }

            // 尝试使用快速合并（仅复制流）
            if (tryFastMerge(listFile.getAbsolutePath(), outPath)) {
                log.info("视频合成成功 >> 输出文件:{}", outPath);
            }

        } finally {
            // 清理临时文件
            for (File tempFile : tempFiles) {
                if (tempFile.exists()) {
                    FileUtil.del(tempFile);
                }
            }
        }
    }

    /**
     * 尝试快速合并（仅复制流，要求编码格式一致）
     */
    private static boolean tryFastMerge(String listFilePath, String outPath) {
        try {
            File outputDir = new File(outPath).getParentFile();
            if (!outputDir.exists()) {
                outputDir.mkdirs();
            }
            // 构建ffmpeg命令
            String[] args = new String[]{
                    FFMPEG_PATH,
                    "-f", "concat",
                    "-safe", "0",
                    "-i", listFilePath,
                    "-c", "copy",
                    outPath,
                    "-y" // 覆盖输出文件
            };

            log.info("执行快速视频合并:{}", StrUtil.join(" ", args));

            // 执行命令
            Process process = new ProcessBuilder(args).redirectErrorStream(true).start();

            // 获取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // 等待命令执行完成
            int exitCode = process.waitFor();

            // 检查命令是否成功执行
            if (exitCode != 0) {
                log.warn("快速视频合并失败 >> 错误信息:{}", output.toString());
                return false;
            }

            return true;
        } catch (Exception e) {
            log.warn("快速视频合并异常 >> 错误信息:{}", e.getMessage());
            return false;
        }
    }

    /**
     * 添加字幕到视频
     *
     * @param videoUrl     视频路径
     * @param subtitlePath 字幕文件路径
     * @param outPath      输出文件路径
     */
    public static void addSubtitle(String videoUrl, String subtitlePath, String outPath) {
        log.info("添加字幕到视频 >> 视频路径:{}, 字幕文件:{}", videoUrl, subtitlePath);
        subtitlePath = subtitlePath.replace("\\", "/");
        String osName = System.getProperty("os.name").toLowerCase();
        // 判断操作系统类型,替换路径
        if (osName.contains("win")) {
            subtitlePath = "C\\:" + subtitlePath.substring(2);
        }
        try {
            // 构建ffmpeg命令，-i 参数指定输入文件，-vf 参数用于添加滤镜（这里是添加字幕），-c copy 参数复制编解码器
            String[] args = new String[]{
                    FFMPEG_PATH,
                    "-y",
                    "-i", videoUrl,
                    "-vf", "subtitles='" + subtitlePath + "'",
                    outPath
            };

            log.info("执行ffmpeg命令:{}", StrUtil.join(" ", args));

            // 执行命令
            Process process = Runtime.getRuntime().exec(args);

            // 获取错误输出
            BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
            String line;
            StringBuilder errorOutput = new StringBuilder();
            while ((line = errorReader.readLine()) != null) {
                errorOutput.append(line).append("\n");
            }

            // 等待命令执行完成
            int exitCode = process.waitFor();

            // 检查命令是否成功执行
            if (exitCode != 0) {
                log.error("添加字幕失败 >> 视频路径:{}, 字幕文件:{}, 错误信息:{}", videoUrl, subtitlePath, errorOutput.toString());
                throw new RuntimeException("添加字幕失败");
            }

            // 返回临时文件的绝对路径
            log.info("添加字幕成功 >> 视频路径:{}, 输出文件:{}", videoUrl, outPath);
        } catch (Exception e) {
            log.error("添加字幕异常 >> 视频路径:{}, 字幕文件:{}", videoUrl, subtitlePath, e);
            throw new RuntimeException("添加字幕异常", e);
        }
    }

    /**
     * 合成ASS字幕到视频（硬字幕烧录）
     *
     * @param videoUrl     视频路径（支持本地文件和网络URL）
     * @param assPath      ASS字幕文件路径
     * @param outPath      输出文件路径
     * @param fontsDir     字体目录路径（可选，为null时使用系统默认字体目录）
     * @param quality      视频质量（CRF值，18-28，数值越小质量越高，可选，默认23）
     */
    public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath, String fontsDir, Integer quality) {
        log.info("合成ASS字幕到视频 >> 视频路径:{}, ASS字幕文件:{}, 输出路径:{}", videoUrl, assPath, outPath);

        if (StrUtil.isBlank(videoUrl) || StrUtil.isBlank(assPath) || StrUtil.isBlank(outPath)) {
            throw new IllegalArgumentException("视频路径、字幕路径和输出路径不能为空");
        }

        // 检查ASS文件是否存在
        File assFile = new File(assPath);
        if (!assFile.exists()) {
            throw new IllegalArgumentException("ASS字幕文件不存在: " + assPath);
        }

        try {
            // 处理路径转义，特别是Windows路径
            String processedAssPath = processPathForFFmpeg(assPath);

            // 构建视频滤镜参数
            StringBuilder filterBuilder = new StringBuilder();
            filterBuilder.append("ass=").append(processedAssPath);

            // 添加字体目录参数
            if (StrUtil.isNotBlank(fontsDir)) {
                String processedFontsDir = processPathForFFmpeg(fontsDir);
                filterBuilder.append(":fontsdir=").append(processedFontsDir);
            } else {
                // 使用系统默认字体目录
                String defaultFontsDir = getDefaultFontsDir();
                if (StrUtil.isNotBlank(defaultFontsDir)) {
                    String processedFontsDir = processPathForFFmpeg(defaultFontsDir);
                    filterBuilder.append(":fontsdir=").append(processedFontsDir);
                }
            }

            // 设置默认质量
            if (quality == null || quality < 0 || quality > 51) {
                quality = 23; // 默认质量
            }

            // 确保输出目录存在
            File outputFile = new File(outPath);
            File outputDir = outputFile.getParentFile();
            if (outputDir != null && !outputDir.exists()) {
                outputDir.mkdirs();
            }

            // 构建ffmpeg命令
            String[] args = new String[]{
                    FFMPEG_PATH,
                    "-i", videoUrl,
                    "-vf", filterBuilder.toString(),
                    "-c:v", "libx264",
                    "-crf", quality.toString(),
                    "-c:a", "copy",
                    "-y", // 覆盖输出文件
                    outPath
            };

            log.info("执行ffmpeg命令:{}", StrUtil.join(" ", args));

            // 使用ProcessBuilder执行命令
            Process process = new ProcessBuilder(args)
                    .redirectErrorStream(true)
                    .start();

            // 读取输出信息
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    // 可以在这里添加进度监控逻辑
                    if (line.contains("time=")) {
                        log.debug("FFmpeg进度: {}", line);
                    }
                }
            }

            // 等待命令执行完成
            int exitCode = process.waitFor();

            // 检查命令是否成功执行
            if (exitCode != 0) {
                log.error("合成ASS字幕失败 >> 视频路径:{}, ASS字幕文件:{}, 错误信息:{}", videoUrl, assPath, output.toString());
                throw new RuntimeException("合成ASS字幕失败，退出码: " + exitCode);
            }

            // 验证输出文件是否生成
            if (!outputFile.exists() || outputFile.length() == 0) {
                throw new RuntimeException("输出文件未生成或为空: " + outPath);
            }

            log.info("合成ASS字幕成功 >> 视频路径:{}, 输出文件:{}, 文件大小:{}KB",
                    videoUrl, outPath, outputFile.length() / 1024);

        } catch (Exception e) {
            log.error("合成ASS字幕异常 >> 视频路径:{}, ASS字幕文件:{}", videoUrl, assPath, e);
            throw new RuntimeException("合成ASS字幕异常", e);
        }
    }

    /**
     * 合成ASS字幕到视频（使用默认参数）
     *
     * @param videoUrl 视频路径
     * @param assPath  ASS字幕文件路径
     * @param outPath  输出文件路径
     */
    public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath) {
        mergeAssSubtitle(videoUrl, assPath, outPath, null, null);
    }

    /**
     * 添加ASS字幕作为软字幕流到视频
     *
     * @param videoUrl 视频路径
     * @param assPath  ASS字幕文件路径
     * @param outPath  输出文件路径（建议使用.mkv格式以获得最佳兼容性）
     * @param language 字幕语言标识（可选，如"chi"、"eng"等）
     * @param title    字幕标题（可选，如"中文"、"English"等）
     */
    public static void addAssSoftSubtitle(String videoUrl, String assPath, String outPath, String language, String title) {
        log.info("添加ASS软字幕到视频 >> 视频路径:{}, ASS字幕文件:{}, 输出路径:{}", videoUrl, assPath, outPath);

        if (StrUtil.isBlank(videoUrl) || StrUtil.isBlank(assPath) || StrUtil.isBlank(outPath)) {
            throw new IllegalArgumentException("视频路径、字幕路径和输出路径不能为空");
        }

        // 检查ASS文件是否存在
        File assFile = new File(assPath);
        if (!assFile.exists()) {
            throw new IllegalArgumentException("ASS字幕文件不存在: " + assPath);
        }

        try {
            // 确保输出目录存在
            File outputFile = new File(outPath);
            File outputDir = outputFile.getParentFile();
            if (outputDir != null && !outputDir.exists()) {
                outputDir.mkdirs();
            }

            // 构建ffmpeg命令参数列表
            List<String> argsList = new ArrayList<>();
            argsList.add(FFMPEG_PATH);
            argsList.add("-i");
            argsList.add(videoUrl);
            argsList.add("-i");
            argsList.add(assPath);
            argsList.add("-c");
            argsList.add("copy");
            argsList.add("-c:s");
            argsList.add("ass");

            // 添加字幕元数据
            if (StrUtil.isNotBlank(language)) {
                argsList.add("-metadata:s:s:0");
                argsList.add("language=" + language);
            }
            if (StrUtil.isNotBlank(title)) {
                argsList.add("-metadata:s:s:0");
                argsList.add("title=" + title);
            }

            argsList.add("-y"); // 覆盖输出文件
            argsList.add(outPath);

            String[] args = argsList.toArray(new String[0]);

            log.info("执行ffmpeg命令:{}", StrUtil.join(" ", args));

            // 使用ProcessBuilder执行命令
            Process process = new ProcessBuilder(args)
                    .redirectErrorStream(true)
                    .start();

            // 读取输出信息
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }

            // 等待命令执行完成
            int exitCode = process.waitFor();

            // 检查命令是否成功执行
            if (exitCode != 0) {
                log.error("添加ASS软字幕失败 >> 视频路径:{}, ASS字幕文件:{}, 错误信息:{}", videoUrl, assPath, output.toString());
                throw new RuntimeException("添加ASS软字幕失败，退出码: " + exitCode);
            }

            // 验证输出文件是否生成
            if (!outputFile.exists() || outputFile.length() == 0) {
                throw new RuntimeException("输出文件未生成或为空: " + outPath);
            }

            log.info("添加ASS软字幕成功 >> 视频路径:{}, 输出文件:{}, 文件大小:{}KB",
                    videoUrl, outPath, outputFile.length() / 1024);

        } catch (Exception e) {
            log.error("添加ASS软字幕异常 >> 视频路径:{}, ASS字幕文件:{}", videoUrl, assPath, e);
            throw new RuntimeException("添加ASS软字幕异常", e);
        }
    }

    /**
     * 添加ASS字幕作为软字幕流到视频（使用默认参数）
     *
     * @param videoUrl 视频路径
     * @param assPath  ASS字幕文件路径
     * @param outPath  输出文件路径
     */
    public static void addAssSoftSubtitle(String videoUrl, String assPath, String outPath) {
        addAssSoftSubtitle(videoUrl, assPath, outPath, null, null);
    }

    /**
     * 处理文件路径以适配FFmpeg的要求
     * 主要处理Windows路径的转义和特殊字符
     *
     * @param path 原始路径
     * @return 处理后的路径
     */
    private static String processPathForFFmpeg(String path) {
        if (StrUtil.isBlank(path)) {
            return path;
        }

        // 统一使用正斜杠
        String processedPath = path.replace("\\", "/");

        // Windows路径处理
        if (isWindows() && processedPath.matches("^[A-Za-z]:.*")) {
            // 将 C:/path 转换为 C\:/path 格式
            processedPath = processedPath.charAt(0) + "\\:" + processedPath.substring(2);
        }

        // 处理路径中的特殊字符，用单引号包围
        if (processedPath.contains(" ") || processedPath.contains("'") || processedPath.contains("(") || processedPath.contains(")")) {
            // 转义单引号
            processedPath = processedPath.replace("'", "'\\''");
            processedPath = "'" + processedPath + "'";
        }

        return processedPath;
    }

    /**
     * 获取系统默认字体目录
     *
     * @return 字体目录路径
     */
    private static String getDefaultFontsDir() {
        String osName = System.getProperty("os.name").toLowerCase();

        if (osName.contains("win")) {
            // Windows系统
            return "C:/Windows/Fonts";
        } else if (osName.contains("mac")) {
            // macOS系统
            return "/System/Library/Fonts";
        } else {
            // Linux系统
            return "/usr/share/fonts";
        }
    }


    private static final String ExecutablePath = "/data";

    /**
     * 获取ffmpeg
     */
    private static File getFFmpegBinary() throws Exception {
        // 根据操作系统确定文件后缀
        String suffix = isWindows() ? ".exe" : "";

        // 创建临时文件
        File ffmpegFile = FileUtil.file(FileUtil.getTmpDirPath() + "/ffmpeg" + suffix);
        if (ffmpegFile.exists()) {
            return ffmpegFile;
        }
        ffmpegFile = FileUtil.touch(FileUtil.getTmpDirPath() + "/ffmpeg" + suffix);


        // 从classpath资源加载ffmpeg
        String fileName = isWindows() ? "ffmpeg.exe" : "ffmpeg";
        ClassPathResource resource = new ClassPathResource(fileName);
        try (InputStream in = resource.getInputStream();
             FileOutputStream out = new FileOutputStream(ffmpegFile)) {
            // 复制资源到临时文件
            StreamUtils.copy(in, out);
        }

        // 非Windows系统设置可执行权限
        if (!isWindows()) {
            if (ffmpegFile.setExecutable(true)) {
                log.info("设置ffmpeg可执行权限成功");
            } else {
                throw new RuntimeException("无法设置ffmpeg可执行权限");
            }
        }
        return ffmpegFile;
    }

    private static boolean isWindows() {
        return System.getProperty("os.name").toLowerCase().contains("win");
    }

    public static void main(String[] args) {
//        String videoUrl = "http://dashscope-result-sh.oss-cn-shanghai.aliyuncs.com/1d/ba/20250624/30eefb8a/1d1f2dbe-13f3-4521-908c-5cc53998dd4e_vidretalk.mp4?Expires=1750839816&OSSAccessKeyId=LTAI5tKPD3TMqf2Lna1fASuh&Signature=5Nb4dmnaC%2BYl4BKsky8Z%2BfHCLdQ%3D";
//        HttpUtil.downloadFile(videoUrl, "C:\\Users\\<USER>\\AppData\\Local\\Temp\\voice\\1935678324591476737\\tt.mp4");
        String videoPath = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\content_combine\\1935678324591476737\\temp_combine.mp4";
        Long duration = getDuration(videoPath);
        System.out.println(duration);
        System.out.println(getVideoCover(videoPath));
        String subtitlePath = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\voice\\1935678324591476737\\audio.srt";
//        addSubtitle(videoPath, subtitlePath);
//        String path = URLUtil.getPath(videoUrl);
//        System.out.println(path);

        // ASS字幕合成测试示例
//        String assPath = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\voice\\1935678324591476737\\subtitle.ass";
//        String outputPath = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\voice\\1935678324591476737\\video_with_ass.mp4";
//
//        // 硬字幕烧录（推荐用于最终输出）
//        mergeAssSubtitle(videoPath, assPath, outputPath);
//
//        // 软字幕嵌入（推荐用于保留字幕可控性）
//        String mkvOutputPath = "C:\\Users\\<USER>\\AppData\\Local\\Temp\\voice\\1935678324591476737\\video_with_soft_ass.mkv";
//        addAssSoftSubtitle(videoPath, assPath, mkvOutputPath, "chi", "中文字幕");
    }


}
