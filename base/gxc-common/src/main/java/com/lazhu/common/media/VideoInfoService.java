package com.lazhu.common.media;

import lombok.extern.slf4j.Slf4j;

import java.io.File;

/**
 * 视频信息服务
 * 负责获取视频的各种信息，如时长、封面等
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class VideoInfoService {

    /**
     * 获取视频/音频时长（秒）
     *
     * @param url 视频URL或路径
     * @return 时长（秒），获取失败返回0
     */
    public static Long getDuration(String url) {
        log.info("获取视频/音频时长 >> url:{}", url);

        // 参数验证
        MediaResourceManager.validateParameters(url);

        // 方法1：尝试使用FFmpeg的信息获取方式
        Long duration = getDurationWithFFmpeg(url);
        if (duration > 0) {
            return duration;
        }

        // 方法2：如果方法1失败，尝试使用另一种方式
        duration = getDurationWithAlternativeMethod(url);
        if (duration > 0) {
            return duration;
        }

        log.error("获取视频/音频时长失败 >> url:{}", url);
        return 0L;
    }

    /**
     * 使用FFmpeg获取时长的第一种方法
     */
    private static Long getDurationWithFFmpeg(String url) {
        try {
            // 使用FFmpeg的-i参数获取信息，重定向到null避免输出文件错误
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .addArg("-i")
                    .addArg(url)
                    .addArgs("-f", "null")
                    .addArg("-")
                    .build();

            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args);

            // FFmpeg在获取信息时通常会返回非0退出码，但输出包含有用信息
            String output = result.getOutput();
            if (output != null && !output.trim().isEmpty()) {
                return parseDurationFromOutput(output);
            }
        } catch (Exception e) {
            log.debug("FFmpeg方法1获取时长失败", e);
        }
        return 0L;
    }

    /**
     * 使用FFmpeg获取时长的备用方法
     */
    private static Long getDurationWithAlternativeMethod(String url) {
        try {
            // 使用FFmpeg的另一种方式：尝试转码到null但获取信息
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(url)
                    .addArgs("-t", "0.1")  // 只处理0.1秒来快速获取信息
                    .addArgs("-f", "null")
                    .addArg("-")
                    .build();

            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args);

            String output = result.getOutput();
            if (output != null && !output.trim().isEmpty()) {
                return parseDurationFromOutput(output);
            }
        } catch (Exception e) {
            log.debug("FFmpeg方法2获取时长失败", e);
        }
        return 0L;
    }

    /**
     * 获取视频第一帧图片作为封面
     * 
     * @param url 视频URL或路径
     * @return 封面图片文件，失败返回null
     */
    public static File getVideoCover(String url) {
        log.info("获取视频第一帧图片 >> url:{}", url);

        // 参数验证
        MediaResourceManager.validateParameters(url);

        try {
            // 创建输出文件
            String fileName = MediaPathUtils.getFileNameWithoutExtension(url);
            File outputFile = MediaResourceManager.createTempFile("video_cover_" + fileName, ".jpg");

            // 构建FFmpeg命令
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(url)
                    .seekStart("00:00:00")
                    .frames(1)
                    .addArgs("-q:v", "2")
                    .overwrite()
                    .output(outputFile.getAbsolutePath())
                    .build();

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("获取视频封面成功 >> url:{}, 封面文件:{}", url, outputFile.getAbsolutePath());
                return outputFile;
            } else {
                log.error("获取视频第一帧图片失败 >> url:{}, 错误信息:{}", url, result.getOutput());
                MediaResourceManager.cleanupTempFile(outputFile);
                return null;
            }
        } catch (Exception e) {
            log.error("获取视频第一帧图片失败 >> url:{}", url, e);
            return null;
        }
    }

    /**
     * 获取视频指定时间点的截图
     * 
     * @param url 视频URL或路径
     * @param timeSeconds 截图时间点（秒）
     * @return 截图文件，失败返回null
     */
    public static File getVideoScreenshot(String url, long timeSeconds) {
        log.info("获取视频截图 >> url:{}, time:{}s", url, timeSeconds);

        // 参数验证
        MediaResourceManager.validateParameters(url);
        if (timeSeconds < 0) {
            throw new IllegalArgumentException("时间点不能为负数");
        }

        try {
            // 创建输出文件
            String fileName = MediaPathUtils.getFileNameWithoutExtension(url);
            File outputFile = MediaResourceManager.createTempFile(
                    "screenshot_" + fileName + "_" + timeSeconds + "s", ".jpg");

            // 构建FFmpeg命令
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(url)
                    .seekStart(String.valueOf(timeSeconds))
                    .frames(1)
                    .addArgs("-q:v", "2")
                    .overwrite()
                    .output(outputFile.getAbsolutePath())
                    .build();

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("获取视频截图成功 >> url:{}, 截图文件:{}", url, outputFile.getAbsolutePath());
                return outputFile;
            } else {
                log.error("获取视频截图失败 >> url:{}, 错误信息:{}", url, result.getOutput());
                MediaResourceManager.cleanupTempFile(outputFile);
                return null;
            }
        } catch (Exception e) {
            log.error("获取视频截图失败 >> url:{}", url, e);
            return null;
        }
    }

    /**
     * 获取视频基本信息
     *
     * @param url 视频URL或路径
     * @return 视频信息对象
     */
    public static VideoInfo getVideoInfo(String url) {
        log.info("获取视频信息 >> url:{}", url);

        // 参数验证
        MediaResourceManager.validateParameters(url);

        // 尝试获取视频信息
        String output = getVideoInfoOutput(url);
        if (output != null && !output.trim().isEmpty()) {
            VideoInfo info = parseVideoInfoFromOutput(output, url);
            if (info.getDuration() > 0 || info.getResolution() != null) {
                return info;
            }
        }

        log.error("获取视频信息失败 >> url:{}", url);
        return new VideoInfo(url, 0L, null, null, null, null);
    }

    /**
     * 获取视频信息的输出
     */
    private static String getVideoInfoOutput(String url) {
        // 方法1：使用-i参数
        try {
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .addArg("-i")
                    .addArg(url)
                    .addArgs("-f", "null")
                    .addArg("-")
                    .build();

            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args);
            String output = result.getOutput();
            if (output != null && !output.trim().isEmpty()) {
                return output;
            }
        } catch (Exception e) {
            log.debug("获取视频信息方法1失败", e);
        }

        // 方法2：使用input方法
        try {
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(url)
                    .addArgs("-t", "0.1")
                    .addArgs("-f", "null")
                    .addArg("-")
                    .build();

            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args);
            return result.getOutput();
        } catch (Exception e) {
            log.debug("获取视频信息方法2失败", e);
        }

        return null;
    }

    /**
     * 从FFmpeg输出中解析时长
     */
    private static Long parseDurationFromOutput(String output) {
        try {
            String[] lines = output.split("\n");
            for (String line : lines) {
                if (line.contains("Duration")) {
                    String[] parts = line.split(",");
                    String durationPart = parts[0].replace("Duration:", "").trim();
                    String[] timeParts = durationPart.split(":");
                    long hours = Long.parseLong(timeParts[0]);
                    long minutes = Long.parseLong(timeParts[1]);
                    double secondsWithMillis = Double.parseDouble(timeParts[2]);
                    return (hours * 3600) + (minutes * 60) + (long) secondsWithMillis;
                }
            }
        } catch (Exception e) {
            log.error("解析时长失败", e);
        }
        return 0L;
    }

    /**
     * 从FFmpeg输出中解析视频信息
     */
    private static VideoInfo parseVideoInfoFromOutput(String output, String url) {
        try {
            String[] lines = output.split("\n");
            
            Long duration = 0L;
            String resolution = null;
            String videoCodec = null;
            String audioCodec = null;
            String bitrate = null;
            
            for (String line : lines) {
                // 解析时长
                if (line.contains("Duration") && duration == 0L) {
                    duration = parseDurationFromOutput(line);
                }
                
                // 解析视频流信息
                if (line.contains("Video:")) {
                    // 解析视频编解码器
                    if (line.contains("h264")) {
                        videoCodec = "H.264";
                    } else if (line.contains("hevc") || line.contains("h265")) {
                        videoCodec = "H.265";
                    } else if (line.contains("vp9")) {
                        videoCodec = "VP9";
                    } else if (line.contains("av1")) {
                        videoCodec = "AV1";
                    }
                    
                    // 解析分辨率
                    if (line.contains("x") && resolution == null) {
                        String[] parts = line.split(",");
                        for (String part : parts) {
                            if (part.trim().matches("\\d+x\\d+")) {
                                resolution = part.trim();
                                break;
                            }
                        }
                    }
                }
                
                // 解析音频流信息
                if (line.contains("Audio:")) {
                    if (line.contains("aac")) {
                        audioCodec = "AAC";
                    } else if (line.contains("mp3")) {
                        audioCodec = "MP3";
                    } else if (line.contains("opus")) {
                        audioCodec = "Opus";
                    } else if (line.contains("vorbis")) {
                        audioCodec = "Vorbis";
                    }
                }
                
                // 解析比特率
                if (line.contains("bitrate:") && bitrate == null) {
                    String[] parts = line.split("bitrate:");
                    if (parts.length > 1) {
                        bitrate = parts[1].trim().split(" ")[0];
                    }
                }
            }
            
            return new VideoInfo(url, duration, resolution, videoCodec, audioCodec, bitrate);
            
        } catch (Exception e) {
            log.error("解析视频信息失败", e);
            return new VideoInfo(url, 0L, null, null, null, null);
        }
    }

    /**
     * 视频信息类
     */
    public static class VideoInfo {
        private final String url;
        private final Long duration;
        private final String resolution;
        private final String videoCodec;
        private final String audioCodec;
        private final String bitrate;

        public VideoInfo(String url, Long duration, String resolution, 
                        String videoCodec, String audioCodec, String bitrate) {
            this.url = url;
            this.duration = duration;
            this.resolution = resolution;
            this.videoCodec = videoCodec;
            this.audioCodec = audioCodec;
            this.bitrate = bitrate;
        }

        public String getUrl() { return url; }
        public Long getDuration() { return duration; }
        public String getResolution() { return resolution; }
        public String getVideoCodec() { return videoCodec; }
        public String getAudioCodec() { return audioCodec; }
        public String getBitrate() { return bitrate; }

        /**
         * 获取格式化的时长字符串 (HH:MM:SS)
         */
        public String getFormattedDuration() {
            if (duration == null || duration <= 0) {
                return "00:00:00";
            }
            
            long hours = duration / 3600;
            long minutes = (duration % 3600) / 60;
            long seconds = duration % 60;
            
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
        }

        @Override
        public String toString() {
            return String.format("VideoInfo{url='%s', duration=%ds, resolution='%s', " +
                               "videoCodec='%s', audioCodec='%s', bitrate='%s'}", 
                               url, duration, resolution, videoCodec, audioCodec, bitrate);
        }
    }
}
