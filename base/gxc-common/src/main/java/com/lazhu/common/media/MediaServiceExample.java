package com.lazhu.common.media;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * 拆分后的媒体服务使用示例
 * 演示如何使用新的服务类进行媒体处理
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class MediaServiceExample {

    public static void main(String[] args) {
        log.info("=== 媒体服务使用示例 ===");
        
        // 示例1：视频信息获取
        videoInfoExample();
        
        // 示例2：视频处理
        videoProcessExample();
        
        // 示例3：字幕处理
        subtitleExample();
        
        // 示例4：资源管理
        resourceManagementExample();
        
        // 示例5：综合应用
        comprehensiveExample();
        
        log.info("=== 示例完成 ===");
    }

    /**
     * 示例1：视频信息获取
     */
    public static void videoInfoExample() {
        log.info("--- 示例1：视频信息获取 ---");
        
        String videoPath = "test_video.mp4";
        
        try {
            // 获取视频时长
            Long duration = VideoInfoService.getDuration(videoPath);
            log.info("视频时长: {}秒", duration);
            
            // 获取视频封面
            File coverFile = VideoInfoService.getVideoCover(videoPath);
            if (coverFile != null) {
                log.info("视频封面: {}", coverFile.getAbsolutePath());
            }
            
            // 获取指定时间点截图
            File screenshot = VideoInfoService.getVideoScreenshot(videoPath, 30);
            if (screenshot != null) {
                log.info("30秒截图: {}", screenshot.getAbsolutePath());
            }
            
            // 获取详细视频信息
            VideoInfoService.VideoInfo info = VideoInfoService.getVideoInfo(videoPath);
            log.info("视频信息: {}", info);
            log.info("格式化时长: {}", info.getFormattedDuration());
            
        } catch (Exception e) {
            log.error("视频信息获取示例失败", e);
        }
    }

    /**
     * 示例2：视频处理
     */
    public static void videoProcessExample() {
        log.info("--- 示例2：视频处理 ---");
        
        try {
            // 视频截取
            String inputVideo = "input_video.mp4";
            String cutResult = VideoProcessService.cut(inputVideo, 10, 60);
            if (cutResult != null) {
                log.info("视频截取成功: {}", cutResult);
            }
            
            // 视频合并
            List<String> videoList = Arrays.asList(
                "video1.mp4",
                "video2.mp4",
                "video3.mp4"
            );
            String mergedOutput = "merged_video.mp4";
            VideoProcessService.merge(videoList, mergedOutput);
            log.info("视频合并完成: {}", mergedOutput);
            
            // 视频格式转换
            String convertInput = "input.avi";
            String convertOutput = "output.mp4";
            VideoProcessService.convert(convertInput, convertOutput, "libx264", "aac", 23);
            log.info("视频转换完成: {}", convertOutput);
            
            // 调整视频分辨率
            String resizeInput = "input_hd.mp4";
            String resizeOutput = "output_720p.mp4";
            VideoProcessService.resize(resizeInput, resizeOutput, 1280, 720);
            log.info("分辨率调整完成: {}", resizeOutput);
            
        } catch (Exception e) {
            log.error("视频处理示例失败", e);
        }
    }

    /**
     * 示例3：字幕处理
     */
    public static void subtitleExample() {
        log.info("--- 示例3：字幕处理 ---");
        
        String videoPath = "video.mp4";
        
        try {
            // 添加SRT字幕（硬字幕）
            String srtPath = "subtitle.srt";
            String srtOutput = "video_with_srt.mp4";
            SubtitleService.addSubtitle(videoPath, srtPath, srtOutput);
            log.info("SRT字幕添加完成: {}", srtOutput);
            
            // 添加ASS字幕（硬字幕）
            String assPath = "subtitle.ass";
            String assOutput = "video_with_ass.mp4";
            SubtitleService.mergeAssSubtitle(videoPath, assPath, assOutput);
            log.info("ASS硬字幕合成完成: {}", assOutput);
            
            // 添加ASS字幕（高质量）
            String hqAssOutput = "video_with_hq_ass.mp4";
            String fontsDir = MediaPathUtils.getDefaultFontsDir();
            SubtitleService.mergeAssSubtitle(videoPath, assPath, hqAssOutput, fontsDir, 18);
            log.info("高质量ASS字幕合成完成: {}", hqAssOutput);
            
            // 添加ASS软字幕
            String softAssOutput = "video_with_soft_ass.mkv";
            SubtitleService.addAssSoftSubtitle(videoPath, assPath, softAssOutput, "chi", "中文字幕");
            log.info("ASS软字幕添加完成: {}", softAssOutput);
            
            // 批量添加多个字幕
            List<SubtitleService.SubtitleInfo> subtitles = Arrays.asList(
                new SubtitleService.SubtitleInfo("chinese.ass", "chi", "中文"),
                new SubtitleService.SubtitleInfo("english.ass", "eng", "English")
            );
            String multiSubOutput = "video_with_multi_subs.mkv";
            SubtitleService.addMultipleSubtitles(videoPath, subtitles, multiSubOutput);
            log.info("多语言字幕添加完成: {}", multiSubOutput);
            
        } catch (Exception e) {
            log.error("字幕处理示例失败", e);
        }
    }

    /**
     * 示例4：资源管理
     */
    public static void resourceManagementExample() {
        log.info("--- 示例4：资源管理 ---");
        
        try {
            // 查看当前资源状态
            MediaResourceManager.TempFileStats stats = MediaResourceManager.getTempFileStats();
            log.info("当前资源状态: {}", stats);
            
            // 创建临时文件
            File tempFile1 = MediaResourceManager.createTempFile("example1", ".tmp");
            File tempFile2 = MediaResourceManager.createTempFile("example2", ".tmp");
            log.info("创建临时文件: {}, {}", tempFile1.getName(), tempFile2.getName());
            
            // 查看更新后的状态
            stats = MediaResourceManager.getTempFileStats();
            log.info("创建文件后状态: {}", stats);
            
            // 手动清理单个文件
            MediaResourceManager.cleanupTempFile(tempFile1);
            log.info("清理文件后状态: 文件数量={}", MediaResourceManager.getTempFileCount());
            
            // 执行维护清理
            MediaResourceManager.performMaintenanceCleanup();
            log.info("维护清理后状态: 文件数量={}", MediaResourceManager.getTempFileCount());
            
            // 演示条件清理
            for (int i = 0; i < 5; i++) {
                MediaResourceManager.createTempFile("batch_" + i, ".tmp");
            }
            log.info("批量创建后文件数量: {}", MediaResourceManager.getTempFileCount());
            
            // 超过数量限制时清理
            MediaResourceManager.cleanupIfExceedsCount(3);
            log.info("条件清理后文件数量: {}", MediaResourceManager.getTempFileCount());
            
        } catch (Exception e) {
            log.error("资源管理示例失败", e);
        }
    }

    /**
     * 示例5：综合应用
     */
    public static void comprehensiveExample() {
        log.info("--- 示例5：综合应用 ---");
        
        try {
            String inputVideo = "source_video.mp4";
            
            // 1. 获取视频信息
            VideoInfoService.VideoInfo info = VideoInfoService.getVideoInfo(inputVideo);
            log.info("处理视频: {}", info);
            
            // 2. 生成封面
            File cover = VideoInfoService.getVideoCover(inputVideo);
            log.info("生成封面: {}", cover != null ? cover.getName() : "失败");
            
            // 3. 截取精彩片段
            String highlight = VideoProcessService.cut(inputVideo, 30, 90);
            log.info("截取精彩片段: {}", highlight != null ? "成功" : "失败");
            
            // 4. 添加字幕
            if (highlight != null) {
                String subtitlePath = "highlight_subtitle.ass";
                String finalOutput = "final_highlight.mp4";
                SubtitleService.mergeAssSubtitle(highlight, subtitlePath, finalOutput);
                log.info("添加字幕完成: {}", finalOutput);
                
                // 5. 转换为不同格式
                String webOutput = "web_version.mp4";
                VideoProcessService.convert(finalOutput, webOutput, "libx264", "aac", 28);
                log.info("Web版本生成: {}", webOutput);
            }
            
            // 6. 资源清理
            log.info("处理前临时文件数量: {}", MediaResourceManager.getTempFileCount());
            MediaResourceManager.performMaintenanceCleanup();
            log.info("清理后临时文件数量: {}", MediaResourceManager.getTempFileCount());
            
        } catch (Exception e) {
            log.error("综合应用示例失败", e);
        }
    }

    /**
     * 演示错误处理和最佳实践
     */
    public static void bestPracticesExample() {
        log.info("--- 最佳实践示例 ---");
        
        // 1. 参数验证
        try {
            VideoInfoService.getDuration(""); // 这会抛出异常
        } catch (IllegalArgumentException e) {
            log.info("参数验证正常工作: {}", e.getMessage());
        }
        
        // 2. 文件存在性检查
        try {
            SubtitleService.addSubtitle("video.mp4", "nonexistent.srt", "output.mp4");
        } catch (IllegalArgumentException e) {
            log.info("文件存在性检查正常工作: {}", e.getMessage());
        }
        
        // 3. 资源管理最佳实践
        File tempFile = null;
        try {
            tempFile = MediaResourceManager.createTempFileUnmanaged("practice", ".tmp");
            // 使用临时文件...
            log.info("使用临时文件: {}", tempFile.getName());
        } finally {
            // 确保清理
            if (tempFile != null) {
                MediaResourceManager.cleanupTempFile(tempFile);
                log.info("手动清理临时文件完成");
            }
        }
        
        // 4. 路径处理
        String windowsPath = "C:\\Users\\<USER>\\video.mp4";
        String ffmpegPath = MediaPathUtils.processPathForFFmpeg(windowsPath);
        log.info("路径转换: {} -> {}", windowsPath, ffmpegPath);
        
        // 5. 系统信息
        log.info("操作系统: {}", MediaPathUtils.getOSType().getDisplayName());
        log.info("默认字体目录: {}", MediaPathUtils.getDefaultFontsDir());
        
        // 6. 监控资源使用
        MediaResourceManager.TempFileStats stats = MediaResourceManager.getTempFileStats();
        log.info("最终资源统计: {}", stats);
    }
}
