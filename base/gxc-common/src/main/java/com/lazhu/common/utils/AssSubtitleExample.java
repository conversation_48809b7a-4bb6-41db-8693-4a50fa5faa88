package com.lazhu.common.utils;

import lombok.extern.slf4j.Slf4j;

/**
 * ASS字幕合成示例类
 * 演示如何使用MediaUtil中的ASS字幕相关方法
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class AssSubtitleExample {

    public static void main(String[] args) {
        // 示例文件路径（请根据实际情况修改）
        String videoPath = "input_video.mp4";
        String assPath = "subtitle.ass";
        
        // 示例1：硬字幕烧录（推荐用于最终发布）
        hardSubtitleExample(videoPath, assPath);
        
        // 示例2：软字幕嵌入（推荐用于保留字幕控制）
        softSubtitleExample(videoPath, assPath);
        
        // 示例3：高质量硬字幕烧录
        highQualityHardSubtitleExample(videoPath, assPath);
        
        // 示例4：指定字体目录的硬字幕烧录
        customFontHardSubtitleExample(videoPath, assPath);
    }
    
    /**
     * 示例1：基本硬字幕烧录
     * 将ASS字幕永久烧录到视频中，所有播放器都能显示
     */
    public static void hardSubtitleExample(String videoPath, String assPath) {
        try {
            String outputPath = "output_hard_subtitle.mp4";
            
            log.info("开始硬字幕烧录...");
            MediaUtil.mergeAssSubtitle(videoPath, assPath, outputPath);
            log.info("硬字幕烧录完成: {}", outputPath);
            
        } catch (Exception e) {
            log.error("硬字幕烧录失败", e);
        }
    }
    
    /**
     * 示例2：软字幕嵌入
     * 将ASS字幕作为独立流嵌入视频，可以开关显示
     */
    public static void softSubtitleExample(String videoPath, String assPath) {
        try {
            String outputPath = "output_soft_subtitle.mkv"; // 建议使用MKV格式
            
            log.info("开始软字幕嵌入...");
            MediaUtil.addAssSoftSubtitle(videoPath, assPath, outputPath, "chi", "中文字幕");
            log.info("软字幕嵌入完成: {}", outputPath);
            
        } catch (Exception e) {
            log.error("软字幕嵌入失败", e);
        }
    }
    
    /**
     * 示例3：高质量硬字幕烧录
     * 使用更高的视频质量设置
     */
    public static void highQualityHardSubtitleExample(String videoPath, String assPath) {
        try {
            String outputPath = "output_hq_hard_subtitle.mp4";
            String fontsDir = null; // 使用系统默认字体目录
            Integer quality = 18; // 高质量设置（18-28，数值越小质量越高）
            
            log.info("开始高质量硬字幕烧录...");
            MediaUtil.mergeAssSubtitle(videoPath, assPath, outputPath, fontsDir, quality);
            log.info("高质量硬字幕烧录完成: {}", outputPath);
            
        } catch (Exception e) {
            log.error("高质量硬字幕烧录失败", e);
        }
    }
    
    /**
     * 示例4：指定字体目录的硬字幕烧录
     * 当ASS字幕使用特殊字体时，需要指定字体目录
     */
    public static void customFontHardSubtitleExample(String videoPath, String assPath) {
        try {
            String outputPath = "output_custom_font_subtitle.mp4";
            String fontsDir = "C:/Windows/Fonts"; // Windows字体目录
            // String fontsDir = "/usr/share/fonts"; // Linux字体目录
            // String fontsDir = "/System/Library/Fonts"; // macOS字体目录
            Integer quality = 23; // 默认质量
            
            log.info("开始自定义字体硬字幕烧录...");
            MediaUtil.mergeAssSubtitle(videoPath, assPath, outputPath, fontsDir, quality);
            log.info("自定义字体硬字幕烧录完成: {}", outputPath);
            
        } catch (Exception e) {
            log.error("自定义字体硬字幕烧录失败", e);
        }
    }
    
    /**
     * 批量处理示例
     * 批量为多个视频添加相同的ASS字幕
     */
    public static void batchProcessExample() {
        String[] videoPaths = {
            "video1.mp4",
            "video2.mp4",
            "video3.mp4"
        };
        
        String assPath = "common_subtitle.ass";
        
        for (int i = 0; i < videoPaths.length; i++) {
            try {
                String outputPath = "output_batch_" + (i + 1) + ".mp4";
                
                log.info("批量处理第{}个视频: {}", i + 1, videoPaths[i]);
                MediaUtil.mergeAssSubtitle(videoPaths[i], assPath, outputPath);
                log.info("批量处理完成: {}", outputPath);
                
            } catch (Exception e) {
                log.error("批量处理第{}个视频失败: {}", i + 1, videoPaths[i], e);
            }
        }
    }
    
    /**
     * 多语言字幕示例
     * 为视频添加多个语言的软字幕
     */
    public static void multiLanguageSubtitleExample(String videoPath) {
        try {
            String chineseAssPath = "subtitle_chinese.ass";
            String englishAssPath = "subtitle_english.ass";
            
            // 先添加中文字幕
            String tempOutputPath = "temp_with_chinese.mkv";
            MediaUtil.addAssSoftSubtitle(videoPath, chineseAssPath, tempOutputPath, "chi", "中文");
            
            // 再添加英文字幕（需要使用更复杂的FFmpeg命令，这里仅作示例）
            String finalOutputPath = "output_multilang.mkv";
            log.info("多语言字幕处理需要更复杂的FFmpeg命令，建议分别处理或使用专业工具");
            
        } catch (Exception e) {
            log.error("多语言字幕处理失败", e);
        }
    }
}
