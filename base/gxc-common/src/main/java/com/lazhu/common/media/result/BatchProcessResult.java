package com.lazhu.common.media.result;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 批量处理结果类
 * 用于封装批量媒体处理操作的结果信息
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class BatchProcessResult {
    
    private final Map<String, ProcessResult> results = new HashMap<>();
    private final List<String> processOrder = new ArrayList<>();
    private long startTime;
    private long endTime;

    public BatchProcessResult() {
        this.startTime = System.currentTimeMillis();
    }

    /**
     * 添加单个处理结果
     */
    public void addResult(String inputFile, ProcessResult result) {
        results.put(inputFile, result);
        processOrder.add(inputFile);
    }

    /**
     * 获取指定文件的处理结果
     */
    public ProcessResult getResult(String inputFile) {
        return results.get(inputFile);
    }

    /**
     * 获取所有处理结果
     */
    public Map<String, ProcessResult> getAllResults() {
        return new HashMap<>(results);
    }

    /**
     * 获取处理顺序列表
     */
    public List<String> getProcessOrder() {
        return new ArrayList<>(processOrder);
    }

    /**
     * 获取总处理数量
     */
    public int getTotalCount() {
        return results.size();
    }

    /**
     * 获取成功处理数量
     */
    public int getSuccessCount() {
        return (int) results.values().stream().filter(ProcessResult::isSuccess).count();
    }

    /**
     * 获取失败处理数量
     */
    public int getFailureCount() {
        return getTotalCount() - getSuccessCount();
    }

    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (getTotalCount() == 0) {
            return 0.0;
        }
        return (double) getSuccessCount() / getTotalCount() * 100.0;
    }

    /**
     * 获取所有成功的结果
     */
    public List<ProcessResult> getSuccessResults() {
        return results.values().stream()
                .filter(ProcessResult::isSuccess)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 获取所有失败的结果
     */
    public List<ProcessResult> getFailureResults() {
        return results.values().stream()
                .filter(result -> !result.isSuccess())
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 获取所有成功的输出文件
     */
    public List<String> getSuccessOutputFiles() {
        return results.values().stream()
                .filter(ProcessResult::isSuccess)
                .map(ProcessResult::getOutputFile)
                .filter(file -> file != null && !file.isEmpty())
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 获取所有失败的输入文件
     */
    public List<String> getFailureInputFiles() {
        return results.entrySet().stream()
                .filter(entry -> !entry.getValue().isSuccess())
                .map(Map.Entry::getKey)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 标记批量处理完成
     */
    public void markCompleted() {
        this.endTime = System.currentTimeMillis();
    }

    /**
     * 获取总处理时间（毫秒）
     */
    public long getTotalProcessingTimeMs() {
        if (endTime == 0) {
            return System.currentTimeMillis() - startTime;
        }
        return endTime - startTime;
    }

    /**
     * 获取总处理时间（秒）
     */
    public double getTotalProcessingTimeSeconds() {
        return getTotalProcessingTimeMs() / 1000.0;
    }

    /**
     * 获取平均处理时间（毫秒）
     */
    public double getAverageProcessingTimeMs() {
        if (getTotalCount() == 0) {
            return 0.0;
        }
        return results.values().stream()
                .mapToLong(ProcessResult::getProcessingTimeMs)
                .average()
                .orElse(0.0);
    }

    /**
     * 获取总输出文件大小（字节）
     */
    public long getTotalOutputSizeBytes() {
        return results.values().stream()
                .filter(ProcessResult::isSuccess)
                .mapToLong(ProcessResult::getOutputFileSizeBytes)
                .sum();
    }

    /**
     * 获取总输出文件大小（MB）
     */
    public double getTotalOutputSizeMB() {
        return getTotalOutputSizeBytes() / (1024.0 * 1024.0);
    }

    /**
     * 获取格式化的总处理时间
     */
    public String getFormattedTotalProcessingTime() {
        long totalMs = getTotalProcessingTimeMs();
        if (totalMs < 1000) {
            return totalMs + " ms";
        } else if (totalMs < 60000) {
            return String.format("%.2f s", totalMs / 1000.0);
        } else {
            long minutes = totalMs / 60000;
            long seconds = (totalMs % 60000) / 1000;
            return String.format("%d min %d s", minutes, seconds);
        }
    }

    /**
     * 获取格式化的总输出大小
     */
    public String getFormattedTotalOutputSize() {
        long totalBytes = getTotalOutputSizeBytes();
        if (totalBytes < 1024) {
            return totalBytes + " B";
        } else if (totalBytes < 1024 * 1024) {
            return String.format("%.2f KB", totalBytes / 1024.0);
        } else if (totalBytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", totalBytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", totalBytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 是否全部成功
     */
    public boolean isAllSuccess() {
        return getFailureCount() == 0 && getTotalCount() > 0;
    }

    /**
     * 是否全部失败
     */
    public boolean isAllFailure() {
        return getSuccessCount() == 0 && getTotalCount() > 0;
    }

    /**
     * 获取详细的统计信息
     */
    public String getDetailedStats() {
        StringBuilder sb = new StringBuilder();
        sb.append("批量处理统计:\n");
        sb.append("  总数量: ").append(getTotalCount()).append("\n");
        sb.append("  成功: ").append(getSuccessCount()).append("\n");
        sb.append("  失败: ").append(getFailureCount()).append("\n");
        sb.append("  成功率: ").append(String.format("%.1f%%", getSuccessRate())).append("\n");
        sb.append("  总处理时间: ").append(getFormattedTotalProcessingTime()).append("\n");
        sb.append("  平均处理时间: ").append(String.format("%.2f s", getAverageProcessingTimeMs() / 1000.0)).append("\n");
        sb.append("  总输出大小: ").append(getFormattedTotalOutputSize());
        return sb.toString();
    }

    @Override
    public String toString() {
        return String.format("BatchProcessResult{total=%d, success=%d, failure=%d, successRate=%.1f%%, totalTime=%s}", 
                getTotalCount(), getSuccessCount(), getFailureCount(), getSuccessRate(), getFormattedTotalProcessingTime());
    }
}
