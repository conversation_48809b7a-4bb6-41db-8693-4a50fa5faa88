package com.lazhu.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * MediaUtil 资源管理示例
 * 演示如何正确使用资源管理功能
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class MediaUtilResourceExample {

    public static void main(String[] args) {
        log.info("=== MediaUtil 资源管理示例 ===");
        
        // 示例1：基本资源管理
        basicResourceManagement();
        
        // 示例2：批量操作的资源管理
        batchOperationResourceManagement();
        
        // 示例3：异常安全的资源管理
        exceptionSafeResourceManagement();
        
        // 示例4：长时间运行应用的资源监控
        longRunningApplicationResourceMonitoring();
        
        // 示例5：手动vs自动资源管理
        manualVsAutomaticResourceManagement();
        
        log.info("=== 示例完成，程序退出时会自动清理剩余资源 ===");
    }

    /**
     * 示例1：基本资源管理
     */
    public static void basicResourceManagement() {
        log.info("--- 示例1：基本资源管理 ---");
        
        try {
            // 获取视频封面（自动管理）
            String videoPath = "test_video.mp4";
            File coverFile = MediaUtil.getVideoCover(videoPath);
            
            if (coverFile != null) {
                log.info("生成封面文件: {}", coverFile.getAbsolutePath());
                log.info("当前临时文件数量: {}", MediaUtil.getTempFileCount());
                
                // 如果需要立即清理
                MediaUtil.cleanupTempFile(coverFile);
                log.info("清理后临时文件数量: {}", MediaUtil.getTempFileCount());
            }
            
        } catch (Exception e) {
            log.error("基本资源管理示例失败", e);
        }
    }

    /**
     * 示例2：批量操作的资源管理
     */
    public static void batchOperationResourceManagement() {
        log.info("--- 示例2：批量操作的资源管理 ---");
        
        List<String> videoUrls = List.of(
            "video1.mp4",
            "video2.mp4", 
            "video3.mp4"
        );
        
        try {
            // 批量处理视频
            for (String videoUrl : videoUrls) {
                try {
                    // 截取视频片段
                    String cutResult = MediaUtil.cut(videoUrl, 10, 30);
                    if (cutResult != null) {
                        log.info("截取视频成功: {}", cutResult);
                    }
                } catch (Exception e) {
                    log.error("处理视频失败: {}", videoUrl, e);
                }
            }
            
            log.info("批量操作完成，当前临时文件数量: {}", MediaUtil.getTempFileCount());
            
            // 批量清理（可选）
            MediaUtil.cleanupAllTempFiles();
            log.info("批量清理完成，当前临时文件数量: {}", MediaUtil.getTempFileCount());
            
        } catch (Exception e) {
            log.error("批量操作资源管理示例失败", e);
        }
    }

    /**
     * 示例3：异常安全的资源管理
     */
    public static void exceptionSafeResourceManagement() {
        log.info("--- 示例3：异常安全的资源管理 ---");
        
        String videoPath = "test_video.mp4";
        String assPath = "test_subtitle.ass";
        String outputPath = "output_with_subtitle.mp4";
        
        try {
            // 尝试合成字幕（可能失败）
            MediaUtil.mergeAssSubtitle(videoPath, assPath, outputPath);
            log.info("字幕合成成功");
            
        } catch (Exception e) {
            log.error("字幕合成失败，但资源已自动清理", e);
            
            // 验证失败的输出文件是否被清理
            File outputFile = new File(outputPath);
            if (!outputFile.exists()) {
                log.info("失败的输出文件已被自动清理");
            }
        }
        
        log.info("异常安全测试完成，当前临时文件数量: {}", MediaUtil.getTempFileCount());
    }

    /**
     * 示例4：长时间运行应用的资源监控
     */
    public static void longRunningApplicationResourceMonitoring() {
        log.info("--- 示例4：长时间运行应用的资源监控 ---");
        
        // 模拟长时间运行的应用
        for (int i = 0; i < 5; i++) {
            try {
                // 模拟视频处理操作
                String videoPath = "video_" + i + ".mp4";
                File coverFile = MediaUtil.getVideoCover(videoPath);
                
                log.info("第{}次操作，当前临时文件数量: {}", i + 1, MediaUtil.getTempFileCount());
                
                // 定期清理策略
                if (MediaUtil.getTempFileCount() > 3) {
                    log.info("临时文件数量超过阈值，执行清理...");
                    MediaUtil.cleanupAllTempFiles();
                    log.info("清理完成，当前临时文件数量: {}", MediaUtil.getTempFileCount());
                }
                
                // 模拟处理间隔
                Thread.sleep(100);
                
            } catch (Exception e) {
                log.error("第{}次操作失败", i + 1, e);
            }
        }
        
        log.info("长时间运行应用资源监控示例完成");
    }

    /**
     * 示例5：手动vs自动资源管理
     */
    public static void manualVsAutomaticResourceManagement() {
        log.info("--- 示例5：手动vs自动资源管理 ---");
        
        // 自动管理示例
        log.info("自动管理示例:");
        try {
            File coverFile = MediaUtil.getVideoCover("auto_managed_video.mp4");
            if (coverFile != null) {
                log.info("自动管理的文件: {}", coverFile.getAbsolutePath());
                log.info("文件会在JVM退出时自动清理");
            }
        } catch (Exception e) {
            log.error("自动管理示例失败", e);
        }
        
        // 手动管理示例
        log.info("手动管理示例:");
        File tempFile = null;
        try {
            // 这里应该使用实际的createTempFileUnmanaged方法
            // tempFile = MediaUtil.createTempFileUnmanaged("manual", ".mp4");
            log.info("手动管理的文件创建完成");
            
            // 使用文件进行操作...
            log.info("使用临时文件进行操作...");
            
        } catch (Exception e) {
            log.error("手动管理示例失败", e);
        } finally {
            // 手动清理
            if (tempFile != null) {
                MediaUtil.cleanupTempFile(tempFile);
                log.info("手动清理完成");
            }
        }
        
        log.info("手动vs自动资源管理示例完成");
    }

    /**
     * 演示资源清理的最佳实践
     */
    public static void demonstrateBestPractices() {
        log.info("--- 资源清理最佳实践 ---");
        
        // 最佳实践1：使用try-finally确保清理
        bestPractice1_TryFinally();
        
        // 最佳实践2：批量操作的资源管理
        bestPractice2_BatchOperations();
        
        // 最佳实践3：异常情况下的资源清理
        bestPractice3_ExceptionHandling();
        
        // 最佳实践4：资源使用监控
        bestPractice4_ResourceMonitoring();
    }

    private static void bestPractice1_TryFinally() {
        log.info("最佳实践1：使用try-finally确保清理");
        
        File tempFile = null;
        try {
            // 创建临时文件
            // tempFile = createTempFileUnmanaged("practice1", ".tmp");
            log.info("创建临时文件用于处理");
            
            // 执行操作
            log.info("执行文件操作...");
            
        } catch (Exception e) {
            log.error("操作失败", e);
        } finally {
            // 确保清理
            if (tempFile != null) {
                MediaUtil.cleanupTempFile(tempFile);
                log.info("finally块中清理临时文件");
            }
        }
    }

    private static void bestPractice2_BatchOperations() {
        log.info("最佳实践2：批量操作的资源管理");
        
        List<File> tempFiles = new ArrayList<>();
        try {
            // 创建多个临时文件
            for (int i = 0; i < 3; i++) {
                // File tempFile = createTempFileUnmanaged("batch_" + i, ".tmp");
                // tempFiles.add(tempFile);
                log.info("创建批量临时文件 {}", i);
            }
            
            // 执行批量操作
            log.info("执行批量操作...");
            
        } catch (Exception e) {
            log.error("批量操作失败", e);
        } finally {
            // 批量清理
            for (File tempFile : tempFiles) {
                MediaUtil.cleanupTempFile(tempFile);
            }
            log.info("批量清理 {} 个临时文件", tempFiles.size());
        }
    }

    private static void bestPractice3_ExceptionHandling() {
        log.info("最佳实践3：异常情况下的资源清理");
        
        try {
            // 可能抛出异常的操作
            log.info("执行可能失败的操作...");
            
            // 模拟异常
            if (Math.random() > 0.5) {
                throw new RuntimeException("模拟异常");
            }
            
        } catch (Exception e) {
            log.error("操作失败，执行异常清理", e);
            
            // 异常情况下的特殊清理
            MediaUtil.cleanupAllTempFiles();
            log.info("异常情况下清理所有临时文件");
        }
    }

    private static void bestPractice4_ResourceMonitoring() {
        log.info("最佳实践4：资源使用监控");
        
        // 监控资源使用
        int tempFileCount = MediaUtil.getTempFileCount();
        log.info("当前临时文件数量: {}", tempFileCount);
        
        // 设置清理阈值
        int threshold = 10;
        if (tempFileCount > threshold) {
            log.warn("临时文件数量超过阈值 {}，执行清理", threshold);
            MediaUtil.cleanupAllTempFiles();
            log.info("清理完成，当前临时文件数量: {}", MediaUtil.getTempFileCount());
        }
        
        // 可以集成到监控系统
        log.info("资源监控信息可以集成到应用监控系统中");
    }
}
