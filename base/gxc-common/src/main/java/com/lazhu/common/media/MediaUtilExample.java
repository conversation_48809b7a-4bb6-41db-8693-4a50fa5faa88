package com.lazhu.common.media;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.net.URL;
import java.util.Arrays;
import java.util.List;

/**
 * MediaUtil 使用示例
 * 演示如何使用 MediaUtil 进行各种媒体处理操作
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class MediaUtilExample {

    public static void main(String[] args) {
        log.info("=== MediaUtil 使用示例 ===");
        
        // 示例1：基础视频信息获取
//        basicVideoInfoExample();
        
        // 示例2：视频处理操作
        videoProcessingExample();
        
        // 示例3：字幕处理
//        subtitleProcessingExample();

        
        log.info("=== 示例完成 ===");
    }

    /**
     * 示例1：基础视频信息获取
     */
    public static void basicVideoInfoExample() {
        log.info("--- 示例1：基础视频信息获取 ---");
        URL resource = MediaUtilExample.class.getClassLoader().getResource("example_video.mp4");

        String videoPath = FileUtil.file(resource).getAbsolutePath();
        
        try {
            // 获取视频时长
            Long duration = MediaUtil.getDuration(videoPath);
            log.info("视频时长: {}秒", duration);
            
            // 获取详细视频信息
            VideoInfoService.VideoInfo info = MediaUtil.getVideoInfo(videoPath);
            log.info("视频信息: {}", info);
            log.info("格式化时长: {}", info.getFormattedDuration());
            log.info("分辨率: {}", info.getResolution());
            log.info("视频编码: {}", info.getVideoCodec());
            log.info("音频编码: {}", info.getAudioCodec());
            
            // 生成视频封面
            File cover = MediaUtil.getVideoCover(videoPath);
            if (cover != null) {
                log.info("封面生成成功: {}", cover.getAbsolutePath());
            }
            
            // 获取指定时间点截图
            File screenshot = MediaUtil.getVideoScreenshot(videoPath, 30);
            if (screenshot != null) {
                log.info("30秒截图生成成功: {}", screenshot.getAbsolutePath());
            }
            
        } catch (Exception e) {
            log.error("视频信息获取失败", e);
        }
    }

    /**
     * 示例2：视频处理操作
     */
    public static void videoProcessingExample() {
        log.info("--- 示例2：视频处理操作 ---");
        
        try {
            // 视频截取
            URL resource = MediaUtilExample.class.getClassLoader().getResource("example_video.mp4");

            String inputVideo = FileUtil.file(resource).getAbsolutePath();
            String cutResult1 = MediaUtil.cut(inputVideo, 0, 30);
            if (cutResult1 != null) {
                log.info("视频截取成功1: {}", cutResult1);
            }
            String cutResult2 = MediaUtil.cut(inputVideo, 31, MediaUtil.getDuration(inputVideo));
            if (cutResult2 != null) {
                log.info("视频截取成功2: {}", cutResult2);
            }
            
            // 视频合并
//            List<String> videoList = Arrays.asList(
//                "C:\\Users\\<USER>\\AppData\\Local\\Temp\\example_video_cut_0_30_1754307424375.mp4",
//                "C:\\Users\\<USER>\\AppData\\Local\\Temp\\example_video_cut_31_41_1754307427942.mp4"
//            );
//            String mergedOutput = "merged_video.mp4";
//            MediaUtil.merge(videoList, mergedOutput);
//            log.info("视频合并完成: {}", mergedOutput);
//
//            // 视频格式转换
//            String convertInput = "input.avi";
//            String convertOutput = "output.mp4";
//            MediaUtil.convert(convertInput, convertOutput, "libx264", "aac", 23);
//            log.info("视频转换完成: {}", convertOutput);
//
//            // 调整视频分辨率
//            String resizeInput = "input_hd.mp4";
//            String resizeOutput = "output_720p.mp4";
//            MediaUtil.resize(resizeInput, resizeOutput, 1280, 720);
//            log.info("分辨率调整完成: {}", resizeOutput);
            
        } catch (Exception e) {
            log.error("视频处理失败", e);
        }
    }

    /**
     * 示例3：字幕处理
     */
    public static void subtitleProcessingExample() {
        log.info("--- 示例3：字幕处理 ---");
        
        String videoPath = "video.mp4";
        
        try {
            // 添加SRT字幕（硬字幕）
            String srtPath = "subtitle.srt";
            String srtOutput = "video_with_srt.mp4";
            MediaUtil.addSubtitle(videoPath, srtPath, srtOutput);
            log.info("SRT字幕添加完成: {}", srtOutput);
            
            // 添加ASS字幕（硬字幕）
            String assPath = "subtitle.ass";
            String assOutput = "video_with_ass.mp4";
            MediaUtil.mergeAssSubtitle(videoPath, assPath, assOutput);
            log.info("ASS硬字幕合成完成: {}", assOutput);
            
            // 添加ASS字幕（高质量，指定字体目录）
            String hqAssOutput = "video_with_hq_ass.mp4";
            MediaUtil.mergeAssSubtitle(videoPath, assPath, hqAssOutput);
            log.info("高质量ASS字幕合成完成: {}", hqAssOutput);
            
            // 添加ASS软字幕
            String softAssOutput = "video_with_soft_ass.mkv";
            MediaUtil.addAssSoftSubtitle(videoPath, assPath, softAssOutput, "chi", "中文字幕");
            log.info("ASS软字幕添加完成: {}", softAssOutput);
            
            // 批量添加多个字幕
            List<SubtitleService.SubtitleInfo> subtitles = Arrays.asList(
                new SubtitleService.SubtitleInfo("chinese.ass", "chi", "中文"),
                new SubtitleService.SubtitleInfo("english.ass", "eng", "English")
            );
            String multiSubOutput = "video_with_multi_subs.mkv";
            MediaUtil.addMultipleSubtitles(videoPath, subtitles, multiSubOutput);
            log.info("多语言字幕添加完成: {}", multiSubOutput);
            
        } catch (Exception e) {
            log.error("字幕处理失败", e);
        }
    }
}
