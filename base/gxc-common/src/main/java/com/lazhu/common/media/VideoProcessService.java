package com.lazhu.common.media;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 视频处理服务
 * 负责视频的截取、合并、转换等处理操作
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class VideoProcessService {

    /**
     * 媒体截取
     *
     * @param url   视频地址
     * @param start 开始时间（秒）
     * @param end   截取结束时间（秒）
     * @return 截取后的文件路径，失败返回null
     */
    public static String cut(String url, long start, long end) {
        log.info("截取媒体文件 >> url:{}, start:{}, end:{}", url, start, end);

        // 参数验证
        MediaResourceManager.validateParameters(url);
        if (start < 0 || end <= start) {
            throw new IllegalArgumentException("时间参数无效：start=" + start + ", end=" + end);
        }

        try {
            // 创建输出文件
            String fileName = MediaPathUtils.getFileNameWithoutExtension(url);
            String extension = MediaPathUtils.getFileExtension(url);
            if (extension.isEmpty()) {
                extension = ".mp4"; // 默认扩展名
            }
            
            File outputFile = MediaResourceManager.createTempFile(
                    fileName + "_cut_" + start + "_" + end, extension);

            // 构建FFmpeg命令
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .seekStart(String.valueOf(start))
                    .input(url)
                    .duration(String.valueOf(end - start))
                    .addArgs("-c", "copy")
                    .overwrite()
                    .output(outputFile.getAbsolutePath())
                    .build();

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("截取媒体文件成功 >> url:{}, 输出文件:{}", url, outputFile.getAbsolutePath());
                return outputFile.getAbsolutePath();
            } else {
                log.error("截取媒体文件失败 >> url:{}, 错误信息:{}", url, result.getOutput());
                MediaResourceManager.cleanupTempFile(outputFile);
                return null;
            }
        } catch (Exception e) {
            log.error("截取媒体文件异常 >> url:{}", url, e);
            throw new RuntimeException("截取媒体文件异常", e);
        }
    }

    /**
     * 视频合并
     *
     * @param urls    输入视频文件路径列表（支持本地文件路径和网络URL）
     * @param outPath 输出文件路径
     */
    public static void merge(List<String> urls, String outPath) {
        log.info("视频合成 >> 输入文件:{}, 输出路径:{}", StrUtil.join(", ", urls), outPath);

        // 参数验证
        if (urls == null || urls.isEmpty()) {
            throw new IllegalArgumentException("输入视频列表不能为空");
        }
        MediaResourceManager.validateParameters(outPath);

        if (urls.size() == 1) {
            // 只有一个视频，直接复制或下载
            handleSingleVideo(urls.get(0), outPath);
            return;
        }

        // 多个视频需要合并
        mergeMultipleVideos(urls, outPath);
    }

    /**
     * 处理单个视频的情况
     */
    private static void handleSingleVideo(String sourceUrl, String outPath) {
        try {
            MediaResourceManager.ensureOutputDirectory(outPath);
            
            if (MediaPathUtils.isNetworkUrl(sourceUrl)) {
                // 网络URL，下载文件
                HttpUtil.downloadFile(sourceUrl, outPath);
            } else {
                // 本地文件，复制文件
                FileUtil.copy(sourceUrl, outPath, true);
            }
            log.info("只有一个视频，直接复制到输出路径 >> 输出文件:{}", outPath);
        } catch (Exception e) {
            log.error("复制单个视频文件失败 >> 错误信息:{}", e.getMessage());
            throw new RuntimeException("复制单个视频文件失败", e);
        }
    }

    /**
     * 合并多个视频
     */
    private static void mergeMultipleVideos(List<String> urls, String outPath) {
        List<String> localVideoPaths = new ArrayList<>();
        List<File> tempFiles = new ArrayList<>();

        try {
            // 下载网络视频到本地
            for (String url : urls) {
                if (MediaPathUtils.isNetworkUrl(url)) {
                    File tempFile = MediaResourceManager.createTempFileUnmanaged("video_merge", ".mp4");
                    HttpUtil.downloadFile(url, tempFile);
                    localVideoPaths.add(tempFile.getAbsolutePath());
                    tempFiles.add(tempFile);
                    log.info("下载网络视频到临时文件 >> URL:{}, 临时文件:{}", url, tempFile.getAbsolutePath());
                } else {
                    localVideoPaths.add(url);
                }
            }

            // 创建列表文件
            File listFile = MediaResourceManager.createTempFileUnmanaged("listfile", ".txt");
            tempFiles.add(listFile);
            
            try (BufferedWriter writer = new BufferedWriter(new FileWriter(listFile))) {
                for (String path : localVideoPaths) {
                    writer.write("file '" + path.replace("'", "'\\''") + "'");
                    writer.newLine();
                }
            } catch (IOException e) {
                log.error("创建列表文件失败 >> 错误信息:{}", e.getMessage());
                throw new RuntimeException("创建列表文件失败", e);
            }

            // 执行合并
            if (tryFastMerge(listFile.getAbsolutePath(), outPath)) {
                log.info("视频合成成功 >> 输出文件:{}", outPath);
            } else {
                throw new RuntimeException("视频合并失败");
            }

        } finally {
            // 清理临时文件
            for (File tempFile : tempFiles) {
                MediaResourceManager.cleanupTempFile(tempFile);
            }
        }
    }

    /**
     * 尝试快速合并（仅复制流，要求编码格式一致）
     */
    private static boolean tryFastMerge(String listFilePath, String outPath) {
        try {
            MediaResourceManager.ensureOutputDirectory(outPath);
            File outputFile = new File(outPath);
            
            // 构建FFmpeg命令
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .addArgs("-f", "concat")
                    .addArgs("-safe", "0")
                    .input(listFilePath)
                    .addArgs("-c", "copy")
                    .overwrite()
                    .output(outPath)
                    .build();

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("快速视频合并成功");
                return true;
            } else {
                log.warn("快速视频合并失败 >> 错误信息:{}", result.getOutput());
                return false;
            }
        } catch (Exception e) {
            log.warn("快速视频合并异常 >> 错误信息:{}", e.getMessage());
            return false;
        }
    }

    /**
     * 视频转换格式
     *
     * @param inputPath  输入文件路径
     * @param outputPath 输出文件路径
     * @param videoCodec 视频编解码器（如 "libx264", "libx265"）
     * @param audioCodec 音频编解码器（如 "aac", "mp3"）
     * @param quality    视频质量（CRF值，18-28）
     */
    public static void convert(String inputPath, String outputPath, String videoCodec, 
                              String audioCodec, Integer quality) {
        log.info("视频格式转换 >> 输入:{}, 输出:{}, 视频编码:{}, 音频编码:{}, 质量:{}", 
                inputPath, outputPath, videoCodec, audioCodec, quality);

        // 参数验证
        MediaResourceManager.validateParameters(inputPath, outputPath);
        MediaResourceManager.validateFileExists(inputPath, "输入文件不存在");

        try {
            MediaResourceManager.ensureOutputDirectory(outputPath);
            File outputFile = new File(outputPath);

            // 设置默认值
            if (StrUtil.isBlank(videoCodec)) {
                videoCodec = "libx264";
            }
            if (StrUtil.isBlank(audioCodec)) {
                audioCodec = "aac";
            }
            if (quality == null || quality < 0 || quality > 51) {
                quality = 23;
            }

            // 构建FFmpeg命令
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(inputPath)
                    .videoCodec(videoCodec)
                    .audioCodec(audioCodec)
                    .quality(quality)
                    .overwrite()
                    .output(outputPath)
                    .build();

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("视频格式转换成功 >> 输出文件:{}, 大小:{}KB", 
                        outputPath, outputFile.length() / 1024);
            } else {
                log.error("视频格式转换失败 >> 错误信息:{}", result.getOutput());
                throw new RuntimeException("视频格式转换失败");
            }
        } catch (Exception e) {
            log.error("视频格式转换异常 >> 输入:{}, 输出:{}", inputPath, outputPath, e);
            throw new RuntimeException("视频格式转换异常", e);
        }
    }

    /**
     * 视频转换格式（使用默认参数）
     */
    public static void convert(String inputPath, String outputPath) {
        convert(inputPath, outputPath, null, null, null);
    }

    /**
     * 调整视频分辨率
     *
     * @param inputPath  输入文件路径
     * @param outputPath 输出文件路径
     * @param width      目标宽度
     * @param height     目标高度
     */
    public static void resize(String inputPath, String outputPath, int width, int height) {
        log.info("调整视频分辨率 >> 输入:{}, 输出:{}, 分辨率:{}x{}", 
                inputPath, outputPath, width, height);

        // 参数验证
        MediaResourceManager.validateParameters(inputPath, outputPath);
        MediaResourceManager.validateFileExists(inputPath, "输入文件不存在");
        if (width <= 0 || height <= 0) {
            throw new IllegalArgumentException("分辨率参数无效：" + width + "x" + height);
        }

        try {
            MediaResourceManager.ensureOutputDirectory(outputPath);
            File outputFile = new File(outputPath);

            // 构建FFmpeg命令
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(inputPath)
                    .videoFilter("scale=" + width + ":" + height)
                    .videoCodec("libx264")
                    .audioCodec("copy")
                    .overwrite()
                    .output(outputPath)
                    .build();

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("调整视频分辨率成功 >> 输出文件:{}, 大小:{}KB", 
                        outputPath, outputFile.length() / 1024);
            } else {
                log.error("调整视频分辨率失败 >> 错误信息:{}", result.getOutput());
                throw new RuntimeException("调整视频分辨率失败");
            }
        } catch (Exception e) {
            log.error("调整视频分辨率异常 >> 输入:{}, 输出:{}", inputPath, outputPath, e);
            throw new RuntimeException("调整视频分辨率异常", e);
        }
    }
}
