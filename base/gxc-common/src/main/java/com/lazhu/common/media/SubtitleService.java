package com.lazhu.common.media;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 字幕服务
 * 负责字幕的添加、合成等操作
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class SubtitleService {

    /**
     * 添加字幕到视频（硬字幕烧录）
     *
     * @param videoUrl     视频路径
     * @param subtitlePath 字幕文件路径
     * @param outPath      输出文件路径
     */
    public static void addSubtitle(String videoUrl, String subtitlePath, String outPath) {
        log.info("添加字幕到视频 >> 视频路径:{}, 字幕文件:{}", videoUrl, subtitlePath);
        
        // 参数验证
        MediaResourceManager.validateParameters(videoUrl, subtitlePath, outPath);
        MediaResourceManager.validateFileExists(subtitlePath, "字幕文件不存在");
        
        try {
            // 处理字幕路径
            String processedSubtitlePath = MediaPathUtils.processPathForFFmpeg(subtitlePath);
            MediaResourceManager.ensureOutputDirectory(outPath);
            File outputFile = new File(outPath);
            
            // 构建FFmpeg命令
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .overwrite()
                    .input(videoUrl)
                    .videoFilter("subtitles='" + processedSubtitlePath + "'")
                    .output(outPath)
                    .build();

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("添加字幕成功 >> 视频路径:{}, 输出文件:{}, 大小:{}KB", 
                        videoUrl, outPath, outputFile.length() / 1024);
            } else {
                log.error("添加字幕失败 >> 视频路径:{}, 字幕文件:{}, 错误信息:{}", 
                        videoUrl, subtitlePath, result.getOutput());
                throw new RuntimeException("添加字幕失败");
            }
        } catch (Exception e) {
            log.error("添加字幕异常 >> 视频路径:{}, 字幕文件:{}", videoUrl, subtitlePath, e);
            throw new RuntimeException("添加字幕异常", e);
        }
    }

    /**
     * 合成ASS字幕到视频（硬字幕烧录）
     *
     * @param videoUrl 视频路径（支持本地文件和网络URL）
     * @param assPath  ASS字幕文件路径
     * @param outPath  输出文件路径
     * @param fontsDir 字体目录路径（可选，为null时使用系统默认字体目录）
     * @param quality  视频质量（CRF值，18-28，数值越小质量越高，可选，默认23）
     */
    public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath, 
                                       String fontsDir, Integer quality) {
        log.info("合成ASS字幕到视频 >> 视频路径:{}, ASS字幕文件:{}, 输出路径:{}", 
                videoUrl, assPath, outPath);
        
        // 参数验证
        MediaResourceManager.validateParameters(videoUrl, assPath, outPath);
        MediaResourceManager.validateFileExists(assPath, "ASS字幕文件不存在");
        
        try {
            MediaResourceManager.ensureOutputDirectory(outPath);
            File outputFile = new File(outPath);
            
            // 构建视频滤镜参数
            String filter = buildAssFilter(assPath, fontsDir);
            
            // 设置默认质量
            if (quality == null || quality < 0 || quality > 51) {
                quality = 23;
            }
            
            // 构建FFmpeg命令
            String[] args = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(videoUrl)
                    .videoFilter(filter)
                    .videoCodec("libx264")
                    .quality(quality)
                    .audioCodec("copy")
                    .overwrite()
                    .output(outPath)
                    .build();
            
            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);
            
            if (result.isSuccess()) {
                log.info("合成ASS字幕成功 >> 视频路径:{}, 输出文件:{}, 文件大小:{}KB", 
                        videoUrl, outPath, outputFile.length() / 1024);
            } else {
                log.error("合成ASS字幕失败 >> 视频路径:{}, ASS字幕文件:{}, 错误信息:{}", 
                        videoUrl, assPath, result.getOutput());
                throw new RuntimeException("合成ASS字幕失败，退出码: " + result.getExitCode());
            }
        } catch (Exception e) {
            log.error("合成ASS字幕异常 >> 视频路径:{}, ASS字幕文件:{}", videoUrl, assPath, e);
            throw new RuntimeException("合成ASS字幕异常", e);
        }
    }
    
    /**
     * 合成ASS字幕到视频（使用默认参数）
     */
    public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath) {
        mergeAssSubtitle(videoUrl, assPath, outPath, null, null);
    }
    
    /**
     * 添加ASS字幕作为软字幕流到视频
     *
     * @param videoUrl 视频路径
     * @param assPath  ASS字幕文件路径
     * @param outPath  输出文件路径（建议使用.mkv格式以获得最佳兼容性）
     * @param language 字幕语言标识（可选，如"chi"、"eng"等）
     * @param title    字幕标题（可选，如"中文"、"English"等）
     */
    public static void addAssSoftSubtitle(String videoUrl, String assPath, String outPath, 
                                         String language, String title) {
        log.info("添加ASS软字幕到视频 >> 视频路径:{}, ASS字幕文件:{}, 输出路径:{}", 
                videoUrl, assPath, outPath);
        
        // 参数验证
        MediaResourceManager.validateParameters(videoUrl, assPath, outPath);
        MediaResourceManager.validateFileExists(assPath, "ASS字幕文件不存在");
        
        try {
            MediaResourceManager.ensureOutputDirectory(outPath);
            File outputFile = new File(outPath);
            
            // 构建FFmpeg命令
            FFmpegExecutor.FFmpegCommandBuilder builder = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(videoUrl)
                    .input(assPath)
                    .addArgs("-c", "copy")
                    .addArgs("-c:s", "ass");
            
            // 添加字幕元数据
            if (StrUtil.isNotBlank(language)) {
                builder.addArgs("-metadata:s:s:0", "language=" + language);
            }
            if (StrUtil.isNotBlank(title)) {
                builder.addArgs("-metadata:s:s:0", "title=" + title);
            }
            
            String[] args = builder.overwrite().output(outPath).build();
            
            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);
            
            if (result.isSuccess()) {
                log.info("添加ASS软字幕成功 >> 视频路径:{}, 输出文件:{}, 文件大小:{}KB", 
                        videoUrl, outPath, outputFile.length() / 1024);
            } else {
                log.error("添加ASS软字幕失败 >> 视频路径:{}, ASS字幕文件:{}, 错误信息:{}", 
                        videoUrl, assPath, result.getOutput());
                throw new RuntimeException("添加ASS软字幕失败，退出码: " + result.getExitCode());
            }
        } catch (Exception e) {
            log.error("添加ASS软字幕异常 >> 视频路径:{}, ASS字幕文件:{}", videoUrl, assPath, e);
            throw new RuntimeException("添加ASS软字幕异常", e);
        }
    }
    
    /**
     * 添加ASS字幕作为软字幕流到视频（使用默认参数）
     */
    public static void addAssSoftSubtitle(String videoUrl, String assPath, String outPath) {
        addAssSoftSubtitle(videoUrl, assPath, outPath, null, null);
    }

    /**
     * 批量添加多个字幕文件
     *
     * @param videoUrl     视频路径
     * @param subtitleInfos 字幕信息列表
     * @param outPath      输出文件路径
     */
    public static void addMultipleSubtitles(String videoUrl, List<SubtitleInfo> subtitleInfos, String outPath) {
        log.info("批量添加字幕 >> 视频路径:{}, 字幕数量:{}, 输出路径:{}", 
                videoUrl, subtitleInfos.size(), outPath);

        // 参数验证
        MediaResourceManager.validateParameters(videoUrl, outPath);
        if (subtitleInfos == null || subtitleInfos.isEmpty()) {
            throw new IllegalArgumentException("字幕信息列表不能为空");
        }

        try {
            MediaResourceManager.ensureOutputDirectory(outPath);
            File outputFile = new File(outPath);

            // 构建FFmpeg命令
            FFmpegExecutor.FFmpegCommandBuilder builder = new FFmpegExecutor.FFmpegCommandBuilder()
                    .input(videoUrl);

            // 添加所有字幕文件作为输入
            for (SubtitleInfo info : subtitleInfos) {
                MediaResourceManager.validateFileExists(info.getPath(), "字幕文件不存在");
                builder.input(info.getPath());
            }

            // 复制视频和音频流
            builder.addArgs("-c:v", "copy")
                   .addArgs("-c:a", "copy");

            // 添加字幕流映射和元数据
            for (int i = 0; i < subtitleInfos.size(); i++) {
                SubtitleInfo info = subtitleInfos.get(i);
                builder.addArgs("-map", "0:v")  // 视频流
                       .addArgs("-map", "0:a")  // 音频流
                       .addArgs("-map", (i + 1) + ":s")  // 字幕流
                       .addArgs("-c:s:" + i, "ass");  // 字幕编码

                // 添加元数据
                if (StrUtil.isNotBlank(info.getLanguage())) {
                    builder.addArgs("-metadata:s:s:" + i, "language=" + info.getLanguage());
                }
                if (StrUtil.isNotBlank(info.getTitle())) {
                    builder.addArgs("-metadata:s:s:" + i, "title=" + info.getTitle());
                }
            }

            String[] args = builder.overwrite().output(outPath).build();

            // 执行命令
            FFmpegExecutor.FFmpegResult result = FFmpegExecutor.executeCommand(args, outputFile);

            if (result.isSuccess()) {
                log.info("批量添加字幕成功 >> 视频路径:{}, 输出文件:{}, 文件大小:{}KB", 
                        videoUrl, outPath, outputFile.length() / 1024);
            } else {
                log.error("批量添加字幕失败 >> 错误信息:{}", result.getOutput());
                throw new RuntimeException("批量添加字幕失败");
            }
        } catch (Exception e) {
            log.error("批量添加字幕异常 >> 视频路径:{}", videoUrl, e);
            throw new RuntimeException("批量添加字幕异常", e);
        }
    }

    /**
     * 构建ASS滤镜参数
     */
    private static String buildAssFilter(String assPath, String fontsDir) {
        String processedAssPath = MediaPathUtils.processPathForFFmpeg(assPath);
        
        StringBuilder filterBuilder = new StringBuilder();
        filterBuilder.append("ass=").append(processedAssPath);
        
        // 添加字体目录参数
        if (StrUtil.isNotBlank(fontsDir)) {
            String processedFontsDir = MediaPathUtils.processPathForFFmpeg(fontsDir);
            filterBuilder.append(":fontsdir=").append(processedFontsDir);
        } else {
            // 使用系统默认字体目录
            String defaultFontsDir = MediaPathUtils.getDefaultFontsDir();
            if (StrUtil.isNotBlank(defaultFontsDir)) {
                String processedFontsDir = MediaPathUtils.processPathForFFmpeg(defaultFontsDir);
                filterBuilder.append(":fontsdir=").append(processedFontsDir);
            }
        }
        
        return filterBuilder.toString();
    }

    /**
     * 字幕信息类
     */
    public static class SubtitleInfo {
        private final String path;
        private final String language;
        private final String title;
        private final SubtitleType type;

        public SubtitleInfo(String path, String language, String title, SubtitleType type) {
            this.path = path;
            this.language = language;
            this.title = title;
            this.type = type;
        }

        public SubtitleInfo(String path, String language, String title) {
            this(path, language, title, SubtitleType.ASS);
        }

        public String getPath() { return path; }
        public String getLanguage() { return language; }
        public String getTitle() { return title; }
        public SubtitleType getType() { return type; }
    }

    /**
     * 字幕类型枚举
     */
    public enum SubtitleType {
        SRT("srt"),
        ASS("ass"),
        VTT("vtt"),
        SUB("sub");

        private final String extension;

        SubtitleType(String extension) {
            this.extension = extension;
        }

        public String getExtension() {
            return extension;
        }
    }
}
