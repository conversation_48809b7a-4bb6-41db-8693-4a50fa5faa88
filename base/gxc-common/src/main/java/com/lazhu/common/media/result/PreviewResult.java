package com.lazhu.common.media.result;

import com.lazhu.common.media.VideoInfoService;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 视频预览结果类
 * 用于封装视频预览生成的结果信息
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class PreviewResult {
    
    private String videoPath;
    private VideoInfoService.VideoInfo videoInfo;
    private File coverFile;
    private Map<Long, File> screenshots = new HashMap<>();
    private boolean success;
    private String errorMessage;
    private long generationTimeMs;

    public PreviewResult() {
        this.generationTimeMs = System.currentTimeMillis();
    }

    // ==================== Getter and Setter ====================

    public String getVideoPath() {
        return videoPath;
    }

    public void setVideoPath(String videoPath) {
        this.videoPath = videoPath;
    }

    public VideoInfoService.VideoInfo getVideoInfo() {
        return videoInfo;
    }

    public void setVideoInfo(VideoInfoService.VideoInfo videoInfo) {
        this.videoInfo = videoInfo;
    }

    public File getCoverFile() {
        return coverFile;
    }

    public void setCoverFile(File coverFile) {
        this.coverFile = coverFile;
    }

    public Map<Long, File> getScreenshots() {
        return new HashMap<>(screenshots);
    }

    public void setScreenshots(Map<Long, File> screenshots) {
        this.screenshots = new HashMap<>(screenshots);
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
        if (success) {
            this.generationTimeMs = System.currentTimeMillis() - this.generationTimeMs;
        }
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public long getGenerationTimeMs() {
        return generationTimeMs;
    }

    public void setGenerationTimeMs(long generationTimeMs) {
        this.generationTimeMs = generationTimeMs;
    }

    // ==================== 便捷方法 ====================

    /**
     * 添加截图
     */
    public void addScreenshot(Long timeSeconds, File screenshotFile) {
        if (timeSeconds != null && screenshotFile != null) {
            screenshots.put(timeSeconds, screenshotFile);
        }
    }

    /**
     * 获取指定时间点的截图
     */
    public File getScreenshot(Long timeSeconds) {
        return screenshots.get(timeSeconds);
    }

    /**
     * 获取所有截图时间点
     */
    public List<Long> getScreenshotTimes() {
        return new ArrayList<>(screenshots.keySet());
    }

    /**
     * 获取所有截图文件
     */
    public List<File> getScreenshotFiles() {
        return new ArrayList<>(screenshots.values());
    }

    /**
     * 获取截图数量
     */
    public int getScreenshotCount() {
        return screenshots.size();
    }

    /**
     * 是否有封面
     */
    public boolean hasCover() {
        return coverFile != null && coverFile.exists();
    }

    /**
     * 是否有截图
     */
    public boolean hasScreenshots() {
        return !screenshots.isEmpty();
    }

    /**
     * 获取生成时间（秒）
     */
    public double getGenerationTimeSeconds() {
        return generationTimeMs / 1000.0;
    }

    /**
     * 获取格式化的生成时间
     */
    public String getFormattedGenerationTime() {
        if (generationTimeMs < 1000) {
            return generationTimeMs + " ms";
        } else if (generationTimeMs < 60000) {
            return String.format("%.2f s", getGenerationTimeSeconds());
        } else {
            long minutes = generationTimeMs / 60000;
            long seconds = (generationTimeMs % 60000) / 1000;
            return String.format("%d min %d s", minutes, seconds);
        }
    }

    /**
     * 获取所有生成文件的总大小（字节）
     */
    public long getTotalFileSizeBytes() {
        long totalSize = 0;
        
        if (coverFile != null && coverFile.exists()) {
            totalSize += coverFile.length();
        }
        
        for (File screenshot : screenshots.values()) {
            if (screenshot != null && screenshot.exists()) {
                totalSize += screenshot.length();
            }
        }
        
        return totalSize;
    }

    /**
     * 获取格式化的总文件大小
     */
    public String getFormattedTotalFileSize() {
        long totalBytes = getTotalFileSizeBytes();
        if (totalBytes < 1024) {
            return totalBytes + " B";
        } else if (totalBytes < 1024 * 1024) {
            return String.format("%.2f KB", totalBytes / 1024.0);
        } else {
            return String.format("%.2f MB", totalBytes / (1024.0 * 1024.0));
        }
    }

    /**
     * 获取预览摘要信息
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append("视频预览摘要:\n");
        
        if (videoInfo != null) {
            sb.append("  视频: ").append(videoPath).append("\n");
            sb.append("  时长: ").append(videoInfo.getFormattedDuration()).append("\n");
            if (videoInfo.getResolution() != null) {
                sb.append("  分辨率: ").append(videoInfo.getResolution()).append("\n");
            }
        }
        
        sb.append("  封面: ").append(hasCover() ? "已生成" : "未生成").append("\n");
        sb.append("  截图数量: ").append(getScreenshotCount()).append("\n");
        sb.append("  生成时间: ").append(getFormattedGenerationTime()).append("\n");
        sb.append("  总文件大小: ").append(getFormattedTotalFileSize());
        
        return sb.toString();
    }

    /**
     * 获取详细信息
     */
    public String getDetailedInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append(getSummary()).append("\n\n");
        
        if (hasCover()) {
            sb.append("封面文件: ").append(coverFile.getAbsolutePath()).append("\n");
        }
        
        if (hasScreenshots()) {
            sb.append("截图文件:\n");
            List<Long> times = new ArrayList<>(screenshots.keySet());
            times.sort(Long::compareTo);
            
            for (Long time : times) {
                File file = screenshots.get(time);
                sb.append("  ").append(time).append("s: ").append(file.getAbsolutePath()).append("\n");
            }
        }
        
        return sb.toString();
    }

    @Override
    public String toString() {
        return String.format("PreviewResult{videoPath='%s', success=%s, cover=%s, screenshots=%d, generationTime=%s}", 
                videoPath, success, hasCover(), getScreenshotCount(), getFormattedGenerationTime());
    }
}
