package com.lazhu.common.media.result;

/**
 * 处理结果类
 * 用于封装媒体处理操作的结果信息
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class ProcessResult {
    
    private boolean success;
    private String message;
    private String errorMessage;
    private String outputFile;
    private String cutFile;
    private String subtitleFile;
    private long processingTimeMs;
    private long outputFileSizeBytes;

    public ProcessResult() {
        this.success = false;
        this.processingTimeMs = System.currentTimeMillis();
    }

    // ==================== Getter and Setter ====================

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
        if (success) {
            this.processingTimeMs = System.currentTimeMillis() - this.processingTimeMs;
        }
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getOutputFile() {
        return outputFile;
    }

    public void setOutputFile(String outputFile) {
        this.outputFile = outputFile;
        // 尝试获取文件大小
        if (outputFile != null) {
            try {
                java.io.File file = new java.io.File(outputFile);
                if (file.exists()) {
                    this.outputFileSizeBytes = file.length();
                }
            } catch (Exception e) {
                // 忽略异常
            }
        }
    }

    public String getCutFile() {
        return cutFile;
    }

    public void setCutFile(String cutFile) {
        this.cutFile = cutFile;
    }

    public String getSubtitleFile() {
        return subtitleFile;
    }

    public void setSubtitleFile(String subtitleFile) {
        this.subtitleFile = subtitleFile;
    }

    public long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    public long getOutputFileSizeBytes() {
        return outputFileSizeBytes;
    }

    public void setOutputFileSizeBytes(long outputFileSizeBytes) {
        this.outputFileSizeBytes = outputFileSizeBytes;
    }

    // ==================== 便捷方法 ====================

    /**
     * 获取输出文件大小（KB）
     */
    public double getOutputFileSizeKB() {
        return outputFileSizeBytes / 1024.0;
    }

    /**
     * 获取输出文件大小（MB）
     */
    public double getOutputFileSizeMB() {
        return outputFileSizeBytes / (1024.0 * 1024.0);
    }

    /**
     * 获取处理时间（秒）
     */
    public double getProcessingTimeSeconds() {
        return processingTimeMs / 1000.0;
    }

    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (outputFileSizeBytes < 1024) {
            return outputFileSizeBytes + " B";
        } else if (outputFileSizeBytes < 1024 * 1024) {
            return String.format("%.2f KB", getOutputFileSizeKB());
        } else if (outputFileSizeBytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", getOutputFileSizeMB());
        } else {
            return String.format("%.2f GB", outputFileSizeBytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 获取格式化的处理时间
     */
    public String getFormattedProcessingTime() {
        if (processingTimeMs < 1000) {
            return processingTimeMs + " ms";
        } else if (processingTimeMs < 60000) {
            return String.format("%.2f s", getProcessingTimeSeconds());
        } else {
            long minutes = processingTimeMs / 60000;
            long seconds = (processingTimeMs % 60000) / 1000;
            return String.format("%d min %d s", minutes, seconds);
        }
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("ProcessResult{");
        sb.append("success=").append(success);
        
        if (success) {
            sb.append(", message='").append(message).append('\'');
            if (outputFile != null) {
                sb.append(", outputFile='").append(outputFile).append('\'');
                sb.append(", fileSize=").append(getFormattedFileSize());
            }
            sb.append(", processingTime=").append(getFormattedProcessingTime());
        } else {
            sb.append(", errorMessage='").append(errorMessage).append('\'');
        }
        
        sb.append('}');
        return sb.toString();
    }
}
