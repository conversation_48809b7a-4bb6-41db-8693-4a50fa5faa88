package com.lazhu.common.media;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.List;

/**
 * 媒体处理门面类
 * 基于门面模式，将所有媒体处理功能集成到一个统一的入口类中
 * 简化调用方式，提供一站式的媒体处理服务
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class MediaUtil {

    // ==================== 视频信息相关方法 ====================

    /**
     * 获取视频/音频时长（秒）
     *
     * @param url 视频URL或路径
     * @return 时长（秒），获取失败返回0
     */
    public static Long getDuration(String url) {
        return VideoInfoService.getDuration(url);
    }

    /**
     * 获取视频第一帧图片作为封面
     *
     * @param url 视频URL或路径
     * @return 封面图片文件，失败返回null
     */
    public static File getVideoCover(String url) {
        return VideoInfoService.getVideoCover(url);
    }

    /**
     * 获取视频指定时间点的截图
     *
     * @param url         视频URL或路径
     * @param timeSeconds 截图时间点（秒）
     * @return 截图文件，失败返回null
     */
    public static File getVideoScreenshot(String url, long timeSeconds) {
        return VideoInfoService.getVideoScreenshot(url, timeSeconds);
    }

    /**
     * 获取视频详细信息
     *
     * @param url 视频URL或路径
     * @return 视频信息对象
     */
    public static VideoInfoService.VideoInfo getVideoInfo(String url) {
        return VideoInfoService.getVideoInfo(url);
    }

    // ==================== 视频处理相关方法 ====================

    /**
     * 媒体截取
     *
     * @param url   视频地址
     * @param start 开始时间（秒）
     * @param end   截取结束时间（秒）
     * @return 截取后的文件路径，失败返回null
     */
    public static String cut(String url, long start, long end) {
        return VideoProcessService.cut(url, start, end);
    }

    /**
     * 视频合并
     *
     * @param urls    输入视频文件路径列表（支持本地文件路径和网络URL）
     * @param outPath 输出文件路径
     */
    public static void merge(List<String> urls, String outPath) {
        VideoProcessService.merge(urls, outPath);
    }

    /**
     * 视频格式转换
     *
     * @param inputPath  输入文件路径
     * @param outputPath 输出文件路径
     * @param videoCodec 视频编解码器（如 "libx264", "libx265"）
     * @param audioCodec 音频编解码器（如 "aac", "mp3"）
     * @param quality    视频质量（CRF值，18-28）
     */
    public static void convert(String inputPath, String outputPath, String videoCodec,
                               String audioCodec, Integer quality) {
        VideoProcessService.convert(inputPath, outputPath, videoCodec, audioCodec, quality);
    }

    /**
     * 视频格式转换（使用默认参数）
     */
    public static void convert(String inputPath, String outputPath) {
        VideoProcessService.convert(inputPath, outputPath);
    }

    /**
     * 调整视频分辨率
     *
     * @param inputPath  输入文件路径
     * @param outputPath 输出文件路径
     * @param width      目标宽度
     * @param height     目标高度
     */
    public static void resize(String inputPath, String outputPath, int width, int height) {
        VideoProcessService.resize(inputPath, outputPath, width, height);
    }

    // ==================== 字幕相关方法 ====================

    /**
     * 添加字幕到视频（硬字幕烧录）
     *
     * @param videoUrl     视频路径
     * @param subtitlePath 字幕文件路径
     * @param outPath      输出文件路径
     */
    public static void addSubtitle(String videoUrl, String subtitlePath, String outPath) {
        SubtitleService.addSubtitle(videoUrl, subtitlePath, outPath);
    }

    /**
     * 合成ASS字幕到视频（硬字幕烧录）
     *
     * @param videoUrl 视频路径（支持本地文件和网络URL）
     * @param assPath  ASS字幕文件路径
     * @param outPath  输出文件路径
     * @param fontsDir 字体目录路径（可选，为null时使用系统默认字体目录）
     * @param quality  视频质量（CRF值，18-28，数值越小质量越高，可选，默认23）
     */
    public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath,
                                        String fontsDir, Integer quality) {
        SubtitleService.mergeAssSubtitle(videoUrl, assPath, outPath, fontsDir, quality);
    }

    /**
     * 合成ASS字幕到视频（使用默认参数）
     */
    public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath) {
        SubtitleService.mergeAssSubtitle(videoUrl, assPath, outPath);
    }

    /**
     * 添加ASS字幕作为软字幕流到视频
     *
     * @param videoUrl 视频路径
     * @param assPath  ASS字幕文件路径
     * @param outPath  输出文件路径（建议使用.mkv格式以获得最佳兼容性）
     * @param language 字幕语言标识（可选，如"chi"、"eng"等）
     * @param title    字幕标题（可选，如"中文"、"English"等）
     */
    public static void addAssSoftSubtitle(String videoUrl, String assPath, String outPath,
                                          String language, String title) {
        SubtitleService.addAssSoftSubtitle(videoUrl, assPath, outPath, language, title);
    }

    /**
     * 添加ASS字幕作为软字幕流到视频（使用默认参数）
     */
    public static void addAssSoftSubtitle(String videoUrl, String assPath, String outPath) {
        SubtitleService.addAssSoftSubtitle(videoUrl, assPath, outPath);
    }

    /**
     * 批量添加多个字幕文件
     *
     * @param videoUrl      视频路径
     * @param subtitleInfos 字幕信息列表
     * @param outPath       输出文件路径
     */
    public static void addMultipleSubtitles(String videoUrl, List<SubtitleService.SubtitleInfo> subtitleInfos, String outPath) {
        SubtitleService.addMultipleSubtitles(videoUrl, subtitleInfos, outPath);
    }

}
