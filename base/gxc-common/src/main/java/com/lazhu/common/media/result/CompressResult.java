package com.lazhu.common.media.result;

/**
 * 压缩结果类
 * 用于封装视频压缩操作的结果信息
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public class CompressResult {
    
    private boolean success;
    private String message;
    private String errorMessage;
    private double originalSizeMB;
    private double targetSizeMB;
    private double finalSizeMB;
    private int qualityUsed;
    private int attemptsUsed;
    private int maxAttempts;
    private long compressionTimeMs;

    public CompressResult() {
        this.compressionTimeMs = System.currentTimeMillis();
    }

    // ==================== Getter and Setter ====================

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
        if (success) {
            this.compressionTimeMs = System.currentTimeMillis() - this.compressionTimeMs;
        }
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public double getOriginalSizeMB() {
        return originalSizeMB;
    }

    public void setOriginalSizeMB(double originalSizeMB) {
        this.originalSizeMB = originalSizeMB;
    }

    public double getTargetSizeMB() {
        return targetSizeMB;
    }

    public void setTargetSizeMB(double targetSizeMB) {
        this.targetSizeMB = targetSizeMB;
    }

    public double getFinalSizeMB() {
        return finalSizeMB;
    }

    public void setFinalSizeMB(double finalSizeMB) {
        this.finalSizeMB = finalSizeMB;
    }

    public int getQualityUsed() {
        return qualityUsed;
    }

    public void setQualityUsed(int qualityUsed) {
        this.qualityUsed = qualityUsed;
    }

    public int getAttemptsUsed() {
        return attemptsUsed;
    }

    public void setAttemptsUsed(int attemptsUsed) {
        this.attemptsUsed = attemptsUsed;
    }

    public int getMaxAttempts() {
        return maxAttempts;
    }

    public void setMaxAttempts(int maxAttempts) {
        this.maxAttempts = maxAttempts;
    }

    public long getCompressionTimeMs() {
        return compressionTimeMs;
    }

    public void setCompressionTimeMs(long compressionTimeMs) {
        this.compressionTimeMs = compressionTimeMs;
    }

    // ==================== 便捷方法 ====================

    /**
     * 获取压缩比例
     */
    public double getCompressionRatio() {
        if (originalSizeMB <= 0) {
            return 0.0;
        }
        return finalSizeMB / originalSizeMB;
    }

    /**
     * 获取压缩率（百分比）
     */
    public double getCompressionRate() {
        return (1.0 - getCompressionRatio()) * 100.0;
    }

    /**
     * 获取节省的空间（MB）
     */
    public double getSavedSizeMB() {
        return originalSizeMB - finalSizeMB;
    }

    /**
     * 是否达到目标大小
     */
    public boolean isTargetAchieved() {
        return success && finalSizeMB <= targetSizeMB * 1.1; // 允许10%的误差
    }

    /**
     * 获取与目标大小的差异（MB）
     */
    public double getTargetDifferenceMB() {
        return finalSizeMB - targetSizeMB;
    }

    /**
     * 获取与目标大小的差异百分比
     */
    public double getTargetDifferencePercent() {
        if (targetSizeMB <= 0) {
            return 0.0;
        }
        return (getTargetDifferenceMB() / targetSizeMB) * 100.0;
    }

    /**
     * 获取压缩时间（秒）
     */
    public double getCompressionTimeSeconds() {
        return compressionTimeMs / 1000.0;
    }

    /**
     * 获取格式化的压缩时间
     */
    public String getFormattedCompressionTime() {
        if (compressionTimeMs < 1000) {
            return compressionTimeMs + " ms";
        } else if (compressionTimeMs < 60000) {
            return String.format("%.2f s", getCompressionTimeSeconds());
        } else {
            long minutes = compressionTimeMs / 60000;
            long seconds = (compressionTimeMs % 60000) / 1000;
            return String.format("%d min %d s", minutes, seconds);
        }
    }

    /**
     * 获取格式化的文件大小信息
     */
    public String getFormattedSizeInfo() {
        return String.format("%.2f MB → %.2f MB (目标: %.2f MB)", 
                originalSizeMB, finalSizeMB, targetSizeMB);
    }

    /**
     * 获取压缩效果评估
     */
    public String getCompressionEffectiveness() {
        if (!success) {
            return "压缩失败";
        }
        
        if (isTargetAchieved()) {
            double rate = getCompressionRate();
            if (rate >= 70) {
                return "优秀压缩";
            } else if (rate >= 50) {
                return "良好压缩";
            } else if (rate >= 30) {
                return "一般压缩";
            } else {
                return "轻微压缩";
            }
        } else {
            return "未达目标";
        }
    }

    /**
     * 获取详细的压缩报告
     */
    public String getDetailedReport() {
        StringBuilder sb = new StringBuilder();
        sb.append("压缩结果报告:\n");
        sb.append("  状态: ").append(success ? "成功" : "失败").append("\n");
        
        if (success) {
            sb.append("  原始大小: ").append(String.format("%.2f MB", originalSizeMB)).append("\n");
            sb.append("  目标大小: ").append(String.format("%.2f MB", targetSizeMB)).append("\n");
            sb.append("  最终大小: ").append(String.format("%.2f MB", finalSizeMB)).append("\n");
            sb.append("  压缩率: ").append(String.format("%.1f%%", getCompressionRate())).append("\n");
            sb.append("  节省空间: ").append(String.format("%.2f MB", getSavedSizeMB())).append("\n");
            sb.append("  质量参数: ").append(qualityUsed).append("\n");
            sb.append("  尝试次数: ").append(attemptsUsed).append("/").append(maxAttempts).append("\n");
            sb.append("  压缩时间: ").append(getFormattedCompressionTime()).append("\n");
            sb.append("  效果评估: ").append(getCompressionEffectiveness()).append("\n");
            
            if (isTargetAchieved()) {
                sb.append("  目标达成: 是");
            } else {
                sb.append("  目标达成: 否 (差异: ").append(String.format("%.2f MB", getTargetDifferenceMB())).append(")");
            }
        } else {
            sb.append("  错误信息: ").append(errorMessage);
        }
        
        return sb.toString();
    }

    @Override
    public String toString() {
        if (success) {
            return String.format("CompressResult{success=true, %s, rate=%.1f%%, quality=%d, attempts=%d, time=%s}", 
                    getFormattedSizeInfo(), getCompressionRate(), qualityUsed, attemptsUsed, getFormattedCompressionTime());
        } else {
            return String.format("CompressResult{success=false, error='%s'}", errorMessage);
        }
    }
}
