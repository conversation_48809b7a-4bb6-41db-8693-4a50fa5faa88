package com.lazhu.common.media;

import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 媒体资源管理器
 * 负责临时文件的创建、跟踪和清理
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class MediaResourceManager {

    /**
     * 临时文件管理器
     */
    private static final List<File> TEMP_FILES = new ArrayList<>();
    
    static {
        // 注册JVM关闭钩子，确保程序退出时清理资源
//        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
//            log.info("JVM关闭，开始清理MediaResourceManager资源...");
//            cleanupAllTempFiles();
//            log.info("MediaResourceManager资源清理完成");
//        }, "MediaResourceManager-Cleanup-Thread"));
    }
    
    /**
     * 创建临时文件并加入管理列表
     */
    public static File createTempFile(String prefix, String suffix) {
        try {
            String tempDir = System.getProperty("java.io.tmpdir");
            File file = new File(tempDir, prefix + "_" + System.currentTimeMillis() + suffix);
            
            // 清理已存在的文件
            if (file.exists()) {
                FileUtil.del(file);
            }
            
            // 添加到临时文件管理列表
            synchronized (TEMP_FILES) {
                TEMP_FILES.add(file);
            }
            
            log.debug("创建临时文件: {}", file.getAbsolutePath());
            return file;
        } catch (Exception e) {
            throw new RuntimeException("创建临时文件失败", e);
        }
    }

    /**
     * 创建临时文件（不加入管理列表）
     */
    public static File createTempFileUnmanaged(String prefix, String suffix) {
        try {
            String tempDir = System.getProperty("java.io.tmpdir");
            File file = new File(tempDir, prefix + "_" + System.currentTimeMillis() + suffix);
            
            // 清理已存在的文件
            if (file.exists()) {
                FileUtil.del(file);
            }
            
            log.debug("创建非管理临时文件: {}", file.getAbsolutePath());
            return file;
        } catch (Exception e) {
            throw new RuntimeException("创建临时文件失败", e);
        }
    }

    /**
     * 清理指定的临时文件
     */
    public static void cleanupTempFile(File file) {
        if (file != null && file.exists()) {
            try {
                FileUtil.del(file);
                synchronized (TEMP_FILES) {
                    TEMP_FILES.remove(file);
                }
                log.debug("清理临时文件: {}", file.getAbsolutePath());
            } catch (Exception e) {
                log.warn("清理临时文件失败: {}", file.getAbsolutePath(), e);
            }
        }
    }

    /**
     * 清理所有临时文件
     */
    public static void cleanupAllTempFiles() {
        synchronized (TEMP_FILES) {
            for (File file : new ArrayList<>(TEMP_FILES)) {
                cleanupTempFile(file);
            }
            TEMP_FILES.clear();
        }
        log.info("已清理所有临时文件");
    }

    /**
     * 获取当前临时文件数量
     */
    public static int getTempFileCount() {
        synchronized (TEMP_FILES) {
            return TEMP_FILES.size();
        }
    }

    /**
     * 获取所有临时文件的总大小（字节）
     */
    public static long getTotalTempFileSize() {
        synchronized (TEMP_FILES) {
            long totalSize = 0;
            for (File file : TEMP_FILES) {
                if (file.exists()) {
                    totalSize += file.length();
                }
            }
            return totalSize;
        }
    }

    /**
     * 清理超过指定大小的临时文件
     * 
     * @param maxSizeBytes 最大总大小（字节）
     */
    public static void cleanupIfExceedsSize(long maxSizeBytes) {
        long currentSize = getTotalTempFileSize();
        if (currentSize > maxSizeBytes) {
            log.info("临时文件总大小 {}KB 超过限制 {}KB，开始清理", 
                    currentSize / 1024, maxSizeBytes / 1024);
            cleanupAllTempFiles();
        }
    }

    /**
     * 清理超过指定数量的临时文件
     * 
     * @param maxCount 最大文件数量
     */
    public static void cleanupIfExceedsCount(int maxCount) {
        int currentCount = getTempFileCount();
        if (currentCount > maxCount) {
            log.info("临时文件数量 {} 超过限制 {}，开始清理", currentCount, maxCount);
            cleanupAllTempFiles();
        }
    }

    /**
     * 获取临时文件统计信息
     */
    public static TempFileStats getTempFileStats() {
        synchronized (TEMP_FILES) {
            int count = TEMP_FILES.size();
            long totalSize = 0;
            int existingCount = 0;
            
            for (File file : TEMP_FILES) {
                if (file.exists()) {
                    existingCount++;
                    totalSize += file.length();
                }
            }
            
            return new TempFileStats(count, existingCount, totalSize);
        }
    }

    /**
     * 临时文件统计信息
     */
    public static class TempFileStats {
        private final int totalCount;
        private final int existingCount;
        private final long totalSize;

        public TempFileStats(int totalCount, int existingCount, long totalSize) {
            this.totalCount = totalCount;
            this.existingCount = existingCount;
            this.totalSize = totalSize;
        }

        public int getTotalCount() { return totalCount; }
        public int getExistingCount() { return existingCount; }
        public long getTotalSize() { return totalSize; }
        public long getTotalSizeKB() { return totalSize / 1024; }
        public long getTotalSizeMB() { return totalSize / (1024 * 1024); }

        @Override
        public String toString() {
            return String.format("TempFileStats{总数=%d, 存在=%d, 大小=%dKB}", 
                    totalCount, existingCount, getTotalSizeKB());
        }
    }

    /**
     * 定期清理任务（可选使用）
     * 建议在长时间运行的应用中定期调用
     */
    public static void performMaintenanceCleanup() {
        TempFileStats stats = getTempFileStats();
        log.info("执行维护清理，当前状态: {}", stats);
        
        // 清理不存在的文件引用
        synchronized (TEMP_FILES) {
            TEMP_FILES.removeIf(file -> !file.exists());
        }
        
        // 如果文件过多或过大，执行清理
        cleanupIfExceedsCount(100);  // 超过100个文件
        cleanupIfExceedsSize(500 * 1024 * 1024);  // 超过500MB
        
        TempFileStats afterStats = getTempFileStats();
        log.info("维护清理完成，清理后状态: {}", afterStats);
    }

    /**
     * 验证参数不为空
     */
    public static void validateParameters(String... params) {
        for (String param : params) {
            if (param == null || param.trim().isEmpty()) {
                throw new IllegalArgumentException("参数不能为空");
            }
        }
    }

    /**
     * 验证文件存在
     */
    public static void validateFileExists(String filePath, String errorMessage) {
        File file = new File(filePath);
        if (!file.exists()) {
            throw new IllegalArgumentException(errorMessage + ": " + filePath);
        }
    }

    /**
     * 确保输出目录存在
     */
    public static void ensureOutputDirectory(String outputPath) {
        File outputFile = new File(outputPath);
        File outputDir = outputFile.getParentFile();
        if (outputDir != null && !outputDir.exists()) {
            outputDir.mkdirs();
            log.debug("创建输出目录: {}", outputDir.getAbsolutePath());
        }
    }
}
