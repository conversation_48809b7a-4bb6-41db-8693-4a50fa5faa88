[Script Info]
Title: 示例ASS字幕
ScriptType: v4.00+
WrapStyle: 0
ScaledBorderAndShadow: yes
YCbCr Matrix: TV.601
PlayResX: 1920
PlayResY: 1080

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,48,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,2,2,10,10,10,1
Style: Title,Arial,60,&H00FFFF00,&H000000FF,&H00000000,&H80000000,1,0,0,0,100,100,0,0,1,3,3,2,10,10,10,1
Style: Subtitle,Microsoft YaHei,42,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,2,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
Dialogue: 0,0:00:01.00,0:00:05.00,Title,,0,0,0,,{\pos(960,100)}欢迎使用ASS字幕
Dialogue: 0,0:00:02.00,0:00:06.00,Default,,0,0,0,,这是一个ASS字幕示例文件
Dialogue: 0,0:00:06.50,0:00:10.50,Subtitle,,0,0,0,,{\c&H00FF00&}绿色文字示例
Dialogue: 0,0:00:11.00,0:00:15.00,Default,,0,0,0,,{\fad(500,500)}淡入淡出效果
Dialogue: 0,0:00:15.50,0:00:19.50,Default,,0,0,0,,{\move(100,540,1820,540)}移动文字效果
Dialogue: 0,0:00:20.00,0:00:24.00,Default,,0,0,0,,{\fs60\b1}大号粗体文字
Dialogue: 0,0:00:24.50,0:00:28.50,Default,,0,0,0,,{\an8}顶部对齐文字
Dialogue: 0,0:00:29.00,0:00:33.00,Default,,0,0,0,,{\blur3}模糊效果文字
Dialogue: 0,0:00:33.50,0:00:37.50,Default,,0,0,0,,{\3c&H0000FF&\bord4}红色边框文字
Dialogue: 0,0:00:38.00,0:00:42.00,Default,,0,0,0,,普通字幕文字示例
