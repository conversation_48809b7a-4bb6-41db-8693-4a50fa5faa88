# MediaUtil 拆分重构说明

## 概述

原 `MediaUtil` 类功能过于庞大，违反了单一职责原则。为了提高代码的可维护性、可测试性和可扩展性，我们将其按功能拆分为多个专门的服务类。

## 拆分架构

### 原始问题
- **单一类过大**：MediaUtil 包含了视频信息获取、视频处理、字幕处理、资源管理等多种功能
- **职责不清**：一个类承担了太多责任
- **难以测试**：功能耦合严重，单元测试困难
- **难以扩展**：添加新功能需要修改庞大的类

### 拆分方案

```
原 MediaUtil (800+ 行)
    ↓
拆分为 6 个专门的类
    ↓
┌─────────────────────────────────────────────────────────────┐
│                    新的架构设计                              │
├─────────────────────────────────────────────────────────────┤
│ FFmpegExecutor        - FFmpeg 执行核心                     │
│ MediaResourceManager  - 资源管理                            │
│ MediaPathUtils        - 路径处理工具                        │
│ VideoInfoService      - 视频信息服务                        │
│ VideoProcessService   - 视频处理服务                        │
│ SubtitleService       - 字幕服务                            │
│ MediaUtilCompat       - 兼容性包装类（向后兼容）             │
└─────────────────────────────────────────────────────────────┘
```

## 各类详细说明

### 1. FFmpegExecutor - FFmpeg 执行核心

**职责**：
- FFmpeg 命令构建
- 进程执行和管理
- 结果处理和资源清理

**主要类**：
```java
// 执行结果
public static class FFmpegResult {
    int getExitCode()
    String getOutput()
    boolean isSuccess()
    File getOutputFile()
}

// 命令构建器
public static class FFmpegCommandBuilder {
    FFmpegCommandBuilder input(String inputPath)
    FFmpegCommandBuilder output(String outputPath)
    FFmpegCommandBuilder videoCodec(String codec)
    FFmpegCommandBuilder quality(int crf)
    // ... 更多方法
    String[] build()
}

// 执行方法
public static FFmpegResult executeCommand(String[] args, File outputFile, Consumer<String> progressCallback)
```

**特点**：
- 流式 API 构建命令
- 统一的结果处理
- 完整的资源清理
- 支持进度回调

### 2. MediaResourceManager - 资源管理

**职责**：
- 临时文件创建和跟踪
- 资源清理和监控
- JVM 关闭钩子管理

**主要方法**：
```java
// 文件管理
public static File createTempFile(String prefix, String suffix)
public static File createTempFileUnmanaged(String prefix, String suffix)
public static void cleanupTempFile(File file)
public static void cleanupAllTempFiles()

// 监控和统计
public static int getTempFileCount()
public static long getTotalTempFileSize()
public static TempFileStats getTempFileStats()

// 维护清理
public static void performMaintenanceCleanup()
public static void cleanupIfExceedsSize(long maxSizeBytes)
public static void cleanupIfExceedsCount(int maxCount)

// 参数验证
public static void validateParameters(String... params)
public static void validateFileExists(String filePath, String errorMessage)
```

**特点**：
- 自动和手动资源管理
- 资源使用监控
- 定期维护清理
- JVM 关闭时自动清理

### 3. MediaPathUtils - 路径处理工具

**职责**：
- 文件路径处理和转换
- 操作系统兼容性处理
- FFmpeg 路径格式转换

**主要方法**：
```java
// 路径处理
public static String processPathForFFmpeg(String path)
public static String normalizePath(String path)
public static String toSystemPath(String path)
public static boolean isAbsolutePath(String path)
public static boolean isNetworkUrl(String path)

// 系统检测
public static boolean isWindows()
public static boolean isMacOS()
public static boolean isLinux()
public static OSType getOSType()

// 文件处理
public static String getFileExtension(String path)
public static String getFileNameWithoutExtension(String path)
public static String buildSafeOutputPath(String directory, String fileName)

// 字体目录
public static String getDefaultFontsDir()
```

**特点**：
- 跨平台路径处理
- FFmpeg 特殊格式支持
- 安全路径验证
- 文件名处理工具

### 4. VideoInfoService - 视频信息服务

**职责**：
- 视频信息获取
- 封面和截图生成
- 媒体文件分析

**主要方法**：
```java
// 基本信息
public static Long getDuration(String url)
public static VideoInfo getVideoInfo(String url)

// 图片生成
public static File getVideoCover(String url)
public static File getVideoScreenshot(String url, long timeSeconds)

// 视频信息类
public static class VideoInfo {
    String getUrl()
    Long getDuration()
    String getResolution()
    String getVideoCodec()
    String getAudioCodec()
    String getBitrate()
    String getFormattedDuration()
}
```

**特点**：
- 专注于信息获取
- 详细的视频信息解析
- 支持封面和截图生成
- 格式化输出支持

### 5. VideoProcessService - 视频处理服务

**职责**：
- 视频截取和合并
- 格式转换和分辨率调整
- 视频编码处理

**主要方法**：
```java
// 基本处理
public static String cut(String url, long start, long end)
public static void merge(List<String> urls, String outPath)

// 格式转换
public static void convert(String inputPath, String outputPath, String videoCodec, String audioCodec, Integer quality)
public static void convert(String inputPath, String outputPath)

// 分辨率调整
public static void resize(String inputPath, String outputPath, int width, int height)
```

**特点**：
- 专注于视频处理
- 支持网络视频下载
- 智能格式检测
- 批量处理支持

### 6. SubtitleService - 字幕服务

**职责**：
- 字幕添加和合成
- ASS 字幕特殊处理
- 多语言字幕支持

**主要方法**：
```java
// 基本字幕处理
public static void addSubtitle(String videoUrl, String subtitlePath, String outPath)

// ASS 字幕处理
public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath, String fontsDir, Integer quality)
public static void mergeAssSubtitle(String videoUrl, String assPath, String outPath)

// 软字幕处理
public static void addAssSoftSubtitle(String videoUrl, String assPath, String outPath, String language, String title)
public static void addAssSoftSubtitle(String videoUrl, String assPath, String outPath)

// 批量字幕处理
public static void addMultipleSubtitles(String videoUrl, List<SubtitleInfo> subtitleInfos, String outPath)

// 字幕信息类
public static class SubtitleInfo {
    String getPath()
    String getLanguage()
    String getTitle()
    SubtitleType getType()
}
```

**特点**：
- 专注于字幕处理
- 支持硬字幕和软字幕
- ASS 字幕特殊支持
- 多语言字幕管理

### 7. MediaUtilCompat - 兼容性包装类

**职责**：
- 保持向后兼容性
- 提供迁移指南
- 委托给新的服务类

**特点**：
- 所有原有方法保持不变
- 内部委托给新的服务类
- 提供迁移提示和指南
- 标记为 @Deprecated

## 迁移指南

### 1. 直接替换（推荐）

**原代码**：
```java
import com.lazhu.common.utils.MediaUtil;

Long duration = MediaUtil.getDuration("video.mp4");
File cover = MediaUtil.getVideoCover("video.mp4");
String cutResult = MediaUtil.cut("video.mp4", 10, 30);
MediaUtil.mergeAssSubtitle("video.mp4", "subtitle.ass", "output.mp4");
MediaUtil.cleanupAllTempFiles();
```

**新代码**：
```java
import com.lazhu.common.media.*;

Long duration = VideoInfoService.getDuration("video.mp4");
File cover = VideoInfoService.getVideoCover("video.mp4");
String cutResult = VideoProcessService.cut("video.mp4", 10, 30);
SubtitleService.mergeAssSubtitle("video.mp4", "subtitle.ass", "output.mp4");
MediaResourceManager.cleanupAllTempFiles();
```

### 2. 使用兼容性包装类（临时方案）

```java
import com.lazhu.common.utils.MediaUtilCompat;

// 现有代码无需修改，但会有 @Deprecated 警告
Long duration = MediaUtilCompat.getDuration("video.mp4");
File cover = MediaUtilCompat.getVideoCover("video.mp4");
```

### 3. 新功能使用

```java
// 获取详细视频信息
VideoInfoService.VideoInfo info = VideoInfoService.getVideoInfo("video.mp4");
System.out.println("分辨率: " + info.getResolution());
System.out.println("编码: " + info.getVideoCodec());

// 视频格式转换
VideoProcessService.convert("input.avi", "output.mp4", "libx264", "aac", 23);

// 调整分辨率
VideoProcessService.resize("input.mp4", "output.mp4", 1920, 1080);

// 资源监控
MediaResourceManager.TempFileStats stats = MediaResourceManager.getTempFileStats();
System.out.println("临时文件统计: " + stats);
```

## 拆分带来的好处

### 1. 单一职责原则
- 每个类只负责一个特定的功能领域
- 代码更加清晰和易于理解
- 降低了类之间的耦合度

### 2. 更好的可测试性
- 可以针对每个服务类编写独立的单元测试
- 测试覆盖率更高，测试更加精确
- 便于模拟和隔离测试

### 3. 更好的可维护性
- 修改某个功能只需要关注对应的服务类
- 减少了修改影响范围
- 代码结构更加清晰

### 4. 更好的可扩展性
- 可以独立扩展每个服务的功能
- 添加新功能不会影响其他服务
- 便于实现插件化架构

### 5. 更好的性能
- 可以针对不同服务进行性能优化
- 资源管理更加精确
- 支持并发处理

## 文件结构

```
src/main/java/com/lazhu/common/
├── media/                          # 新的媒体处理包
│   ├── FFmpegExecutor.java         # FFmpeg 执行核心
│   ├── MediaResourceManager.java   # 资源管理器
│   ├── MediaPathUtils.java         # 路径处理工具
│   ├── VideoInfoService.java       # 视频信息服务
│   ├── VideoProcessService.java    # 视频处理服务
│   └── SubtitleService.java        # 字幕服务
└── utils/                          # 原有工具包
    ├── MediaUtil.java              # 原始类（可选择保留或删除）
    └── MediaUtilCompat.java        # 兼容性包装类
```

## 最佳实践建议

### 1. 逐步迁移
- 新代码直接使用新的服务类
- 现有代码可以先使用兼容性包装类
- 逐步将现有代码迁移到新的服务类

### 2. 依赖注入
```java
@Service
public class VideoService {
    // 可以考虑将这些工具类包装为 Spring Bean
    
    public void processVideo(String videoPath) {
        VideoInfoService.VideoInfo info = VideoInfoService.getVideoInfo(videoPath);
        // 处理逻辑...
    }
}
```

### 3. 异常处理
```java
try {
    VideoProcessService.cut("video.mp4", 10, 30);
} catch (RuntimeException e) {
    log.error("视频处理失败", e);
    // 错误处理逻辑
}
```

### 4. 资源管理
```java
try {
    // 视频处理操作
    String result = VideoProcessService.cut("video.mp4", 10, 30);
    // 使用结果...
} finally {
    // 定期清理资源
    MediaResourceManager.performMaintenanceCleanup();
}
```

## 总结

通过将 `MediaUtil` 拆分为多个专门的服务类，我们实现了：

1. **更好的代码组织**：每个类都有明确的职责
2. **更高的代码质量**：遵循 SOLID 原则
3. **更强的可维护性**：便于修改和扩展
4. **更好的测试支持**：便于编写单元测试
5. **向后兼容性**：现有代码无需立即修改

这种重构方式既保证了代码质量的提升，又确保了现有系统的稳定性。
